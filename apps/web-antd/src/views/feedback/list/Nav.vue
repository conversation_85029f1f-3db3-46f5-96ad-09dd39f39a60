<script lang="ts" setup>
import { buttonVariants } from '@/components/ui/button'

import { Separator } from '@/components/ui/separator'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar'
import {
  <PERSON>ltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'
import { ref } from 'vue'

export interface LinkProp {
  title: string
  label?: string
  value?: string
  icon: string
}

interface NavProps {
  isCollapsed: boolean
  links: LinkProp[]
  activeMenu?: string
}

defineProps<NavProps>()
const emit = defineEmits(['menuClick'])

const selectedMenu = ref('all')

function handleMenuClick(link: LinkProp) {
  selectedMenu.value = link.value ?? 'all'
  emit('menuClick', link)
}
</script>

<template>
  <div
    :data-collapsed="isCollapsed"
    class="group flex flex-col gap-4 py-2 data-[collapsed=true]:py-2"
  >
    <SidebarMenu>
      <template v-for="(link, index) of links" :key="index">
        <Separator v-if="link.value === 'created'" />
        <SidebarMenuItem class="px-2">
          <Tooltip v-if="isCollapsed" :delay-duration="0">
            <TooltipTrigger as-child>
              <SidebarMenuButton
                :class="
                  cn(
                    buttonVariants({
                      variant:
                        selectedMenu === link.value ? 'default' : 'ghost',
                      size: 'sm',
                    }),
                    selectedMenu === link.value
                      && 'dark:bg-muted dark:text-muted-foreground dark:hover:bg-muted dark:hover:text-white',
                  )
                "
                @click="handleMenuClick(link)"
              >
                <component :is="link.icon" :size="16" />
                <span class="sr-only">{{ link.title }}</span>
              </SidebarMenuButton>
            </TooltipTrigger>
            <TooltipContent side="right" class="flex items-center gap-4">
              {{ link.title }}
              <span v-if="link.label" class="ml-auto">
                {{ link.label }}
              </span>
            </TooltipContent>
          </Tooltip>
          <SidebarMenuButton
            v-else
            :class="
              cn(
                buttonVariants({
                  variant: selectedMenu === link.value ? 'default' : 'ghost',
                  size: 'sm',
                }),
                selectedMenu === link.value
                  && 'dark:bg-muted dark:text-muted-foreground dark:hover:bg-muted dark:hover:text-white',
              )
            "
            @click="handleMenuClick(link)"
          >
            <component :is="link.icon" :size="16" />

            <span>{{ link.title }}</span>
            <span :class="cn('ml-auto')">
              {{ link.label }}
            </span>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </template>
    </SidebarMenu>
  </div>
</template>
