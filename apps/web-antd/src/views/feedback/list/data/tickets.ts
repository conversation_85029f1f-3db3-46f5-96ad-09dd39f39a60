export interface Ticket {
  // 基本信息
  id: string
  ticketID?: string // 工单ID（API返回的主要ID字段）
  title?: string // 工单标题（可选，因为API可能不返回）
  text?: string // 工单描述（可选）
  problemDescription?: string // 问题描述（API主要字段）
  date?: string // 日期（可选）
  status?: string // 状态（前端使用）
  stage?: string // 工单阶段（API返回的主要状态字段）
  labels?: string[] // 标签（可选）

  // 时间信息
  startTime?: string // 开始时间
  enterTime?: string // 进入时间
  responseTime?: string // 响应时间
  endTime?: string // 结束时间
  createdAt?: string // 创建时间（可选）
  updatedAt?: string
  dueDate?: string

  // 人员信息
  creator?: string // 创建人（可选）
  handler?: string // 处理人（可选）
  owner?: string[] // 负责人
  businessOwner?: string[] // 业务负责人
  devOwner?: string[] // 开发负责人
  clientIOSOwner?: string[] // iOS客户端负责人
  clientAndroidOwner?: string[] // Android客户端负责人
  respondent?: string // 响应人
  devProcessor?: string // 开发处理人
  feedbackPerson?: string // 反馈人

  // 分类信息
  severityLevel?: string // 严重程度
  functionType?: string[] // 功能类型
  firstLevelCategory?: string // 一级分类
  secondLevelCategory?: string // 二级分类
  thirdLevelCategory?: string // 三级分类
  endType?: string[] // 结束类型

  // 设备信息
  apps?: string[] // 应用
  appVersion?: string[] // 应用版本
  osType?: string[] // 操作系统类型
  mobileType?: string[] // 手机型号
  userTtid?: string[] // 用户TTID

  // 处理信息
  feishuGroup?: string // 飞书群组
  feishuGroupId?: string // 飞书群组ID
  cause?: string // 原因
  result?: string // 结果
  reason?: string // 理由
  hasFeedback?: string // 是否有反馈
  hasWorkingHours?: string // 是否有工时
  hasClientRelease?: string // 是否有客户端发布
  isUploadLogs?: string // 是否上传日志

  // 附件和评论
  attachments?: {
    name: string
    size?: string
    type: string
    uid?: string
    url: string
  }[]

  comments?: {
    creator: string
    date: string
    id: string
    text: string
  }[]

  // 变更日志
  changeLogs?: {
    assignee?: string
    assignor?: string
    content: string
    operator?: string
    time: string
    type: string
  }[]
}

// 构造函数生成随机工单数据
function generateRandomTicket(index: number): Ticket {
  // 随机生成日期（2025年内）
  const randomMonth = Math.floor(Math.random() * 12) + 1
  const randomDay = Math.floor(Math.random() * 28) + 1
  const randomHour = Math.floor(Math.random() * 24)
  const randomMinute = Math.floor(Math.random() * 60)

  const baseDate = `2025-${String(randomMonth).padStart(2, '0')}-${String(randomDay).padStart(2, '0')} ${String(randomHour).padStart(2, '0')}:${String(randomMinute).padStart(2, '0')}`

  // 随机生成前后时间
  const startTimeDate = new Date(
    new Date(baseDate).getTime()
      - Math.floor(Math.random() * 3 * 24 * 60 * 60 * 1000),
  )
  const enterTimeDate = new Date(baseDate)
  const responseTimeDate = new Date(
    new Date(baseDate).getTime()
      + Math.floor(Math.random() * 2 * 60 * 60 * 1000),
  )
  const endTimeDate = new Date(
    new Date(responseTimeDate).getTime()
      + Math.floor(Math.random() * 3 * 60 * 60 * 1000),
  )

  const startTime = `${startTimeDate.getFullYear()}-${String(startTimeDate.getMonth() + 1).padStart(2, '0')}-${String(startTimeDate.getDate()).padStart(2, '0')} ${String(startTimeDate.getHours()).padStart(2, '0')}:${String(startTimeDate.getMinutes()).padStart(2, '0')}`
  const enterTime = baseDate
  const responseTime = `${responseTimeDate.getFullYear()}-${String(responseTimeDate.getMonth() + 1).padStart(2, '0')}-${String(responseTimeDate.getDate()).padStart(2, '0')} ${String(responseTimeDate.getHours()).padStart(2, '0')}:${String(responseTimeDate.getMinutes()).padStart(2, '0')}`
  const endTime = `${endTimeDate.getFullYear()}-${String(endTimeDate.getMonth() + 1).padStart(2, '0')}-${String(endTimeDate.getDate()).padStart(2, '0')} ${String(endTimeDate.getHours()).padStart(2, '0')}:${String(endTimeDate.getMinutes()).padStart(2, '0')}`

  // 生成固定的工单ID（用于测试）
  const ticketID = `${1_000_000_000 + index * 1_000_000_000}${index}`

  // 随机选择状态
  const stages = ['待处理', '处理中', '已处理', '已归档']
  const stage = stages[Math.floor(Math.random() * stages.length)]

  // 随机选择人员
  const owners = [['陈婷'], ['王明'], ['李强'], ['张伟'], ['admin']]
  const creators = ['陈婷', '王明', '李强', '张伟', 'admin']
  const businessOwners = [['刘智敏'], ['张经理'], ['王主管'], ['李总监']]
  const devOwners = [
    ['詹明俊', '陈伟贤'],
    ['李开发', '张工程师'],
    ['王技术', '刘架构师'],
  ]

  // 随机选择问题描述
  const problems = [
    '进k歌房听不到音乐和别人唱歌的声音，切换欢游 、麦可、TT 都没声音',
    '语音房间内麦克风无法使用，显示设备被占用',
    '直播间礼物特效不显示，但是礼物记录有',
    '游戏房间内延迟严重，玩家反馈卡顿',
    '语音通话突然中断，重连后无法加入原房间',
    '房间内背景音乐无法切换，一直播放同一首歌',
    '进入语音房间后自动退出，无法正常使用',
    '房间内其他用户头像不显示，只有默认图标',
    '语音房间内无法调节音量，声音过大',
    '创建房间失败，提示网络异常但网络正常',
  ]
  const problemDescription
    = problems[Math.floor(Math.random() * problems.length)]

  // 随机生成用户ID
  const userTtid = [`${Math.floor(Math.random() * 900_000_000) + 100_000_000}`]

  // 随机决定是否有附件
  const hasAttachment = Math.random() > 0.5
  const attachments = hasAttachment
    ? [
        {
          uid: `${Math.random().toString(36).slice(2, 15)}.${['mp4', 'jpg', 'png'][Math.floor(Math.random() * 3)]}`,
          url: `https://obs-prod-hw-bj-yw-fmp-backend.obs.cn-north-4.myhuaweicloud.com/${Math.random().toString(36).slice(2, 15)}.${['mp4', 'jpg', 'png'][Math.floor(Math.random() * 3)]}`,
          name: `飞书${baseDate.replaceAll(/\D/g, '')}.${['mp4', 'jpg', 'png'][Math.floor(Math.random() * 3)]}`,
          type: ['mp4', 'jpg', 'png'][Math.floor(Math.random() * 3)],
        },
      ]
    : []

  // 随机选择应用和设备信息
  const appsList = [['TT语音'], ['TT直播'], ['TT游戏']]
  const apps = appsList[Math.floor(Math.random() * appsList.length)]

  const appVersions = [
    ['Version 6.65.3 18735'],
    ['Version 6.70.1 19001'],
    ['Version 6.68.2 18900'],
  ]
  const appVersion
    = appVersions[Math.floor(Math.random() * appVersions.length)]

  const osTypes = [['Android'], ['iOS']]
  const osType = osTypes[Math.floor(Math.random() * osTypes.length)]

  const mobileTypes = [
    ['荣耀畅玩40plus'],
    ['小米12'],
    ['华为P40'],
    ['OPPO Find X5'],
    ['vivo X80'],
    ['iPhone 13'],
    ['iPhone 14 Pro'],
  ]
  const mobileType
    = mobileTypes[Math.floor(Math.random() * mobileTypes.length)]

  // 随机选择严重程度
  const severityLevels = ['个例问题', '批量问题', '紧急问题']
  const severityLevel
    = severityLevels[Math.floor(Math.random() * severityLevels.length)]

  // 随机选择功能类型
  const functionTypesList = [
    ['房间玩法', '房间音频'],
    ['直播功能', '礼物系统'],
    ['游戏功能', '匹配系统'],
    ['社交功能', '好友系统'],
    ['账号功能', '登录注册'],
  ]
  const functionType
    = functionTypesList[Math.floor(Math.random() * functionTypesList.length)]

  // 随机选择处理人员
  const respondents = ['刘智敏', '张经理', '王主管', '李总监', 'admin']
  const respondent
    = respondents[Math.floor(Math.random() * respondents.length)]

  const devProcessors = ['卢安', '张开发', '王工程师', '李技术', 'admin']
  const devProcessor
    = devProcessors[Math.floor(Math.random() * devProcessors.length)]

  // 随机生成原因和结果
  const causes = [
    '无日志，无法定位',
    '用户网络问题',
    '服务端异常',
    '客户端版本过低',
    '设备兼容性问题',
    '系统权限未开启',
    '第三方SDK异常',
    '用户操作不当',
    '服务器负载过高',
    '数据同步失败',
  ]
  const cause
    = stage === '已处理' ? causes[Math.floor(Math.random() * causes.length)] : ''

  const results = [
    '1. 建议引导用户发现问题时，马上发反馈，方便获取当天日志\n2. 建议引导检查麦克风是否正常，音量是否正常，是否处于静音状态',
    '1. 已修复服务端异常，问题已解决\n2. 建议用户更新到最新版本',
    '1. 问题已定位，将在下个版本修复\n2. 临时解决方案：重启应用后再试',
    '1. 已优化网络连接策略\n2. 建议用户在网络良好环境下使用',
    '1. 已修复兼容性问题\n2. 特定机型需要手动授权麦克风权限',
    '1. 服务器扩容完成，高峰期不再拥堵\n2. 优化了资源调度逻辑',
    '1. 已更新第三方SDK版本\n2. 修复了已知的崩溃问题',
    '1. 优化了用户操作流程\n2. 添加了更明确的操作提示',
    '1. 修复了数据同步问题\n2. 提高了同步成功率',
    '1. 已解决音频处理问题\n2. 优化了低网速下的音频传输质量',
  ]
  const result
    = stage === '已处理'
      ? results[Math.floor(Math.random() * results.length)]
      : ''

  // 随机选择是否有客户端发布
  const hasClientReleases = ['是', '否']
  const hasClientRelease
    = hasClientReleases[Math.floor(Math.random() * hasClientReleases.length)]

  // 随机选择结束类型
  const endTypesList = [
    ['客户端'],
    ['服务端'],
    ['产品设计'],
    ['用户操作'],
    ['第三方问题'],
  ]
  const endType
    = stage === '已处理' || stage === '已归档'
      ? endTypesList[Math.floor(Math.random() * endTypesList.length)]
      : []

  // 生成变更日志
  const changeLogs = []

  // 创建工单记录
  changeLogs.push({
    time: `${enterTime}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
    type: 'create',
    content: '创建了工单',
    operator: owners[Math.floor(Math.random() * owners.length)][0],
  })

  // 如果状态是处理中或已处理，添加指派记录
  if (stage === '处理中' || stage === '已处理') {
    const assignTime = new Date(
      new Date(enterTime).getTime()
        + Math.floor(Math.random() * 2 * 60 * 60 * 1000),
    )
    const assignTimeStr = `${assignTime.getFullYear()}-${String(assignTime.getMonth() + 1).padStart(2, '0')}-${String(assignTime.getDate()).padStart(2, '0')} ${String(assignTime.getHours()).padStart(2, '0')}:${String(assignTime.getMinutes()).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`

    changeLogs.push({
      time: assignTimeStr,
      type: 'appoint',
      content: '指派了工单',
      assignee: '梁伟健',
      assignor: respondent,
    })

    const assignTime2 = new Date(
      assignTime.getTime() + Math.floor(Math.random() * 60 * 60 * 1000),
    )
    const assignTimeStr2 = `${assignTime2.getFullYear()}-${String(assignTime2.getMonth() + 1).padStart(2, '0')}-${String(assignTime2.getDate()).padStart(2, '0')} ${String(assignTime2.getHours()).padStart(2, '0')}:${String(assignTime2.getMinutes()).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`

    changeLogs.push({
      time: assignTimeStr2,
      type: 'appoint',
      content: '指派了工单',
      assignee: devProcessor,
      assignor: '梁伟健',
    })
  }

  // 如果状态是已处理或已归档，添加处理完成记录
  if (stage === '已处理' || stage === '已归档') {
    changeLogs.push({
      time: `${endTime}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
      type: 'resolved',
      content: '处理完成工单',
      operator: devProcessor,
    })

    // 如果状态是已归档，添加归档记录
    if (stage === '已归档') {
      const archiveTime = new Date(
        new Date(endTime).getTime()
          + Math.floor(Math.random() * 24 * 60 * 60 * 1000),
      )
      const archiveTimeStr = `${archiveTime.getFullYear()}-${String(archiveTime.getMonth() + 1).padStart(2, '0')}-${String(archiveTime.getDate()).padStart(2, '0')} ${String(archiveTime.getHours()).padStart(2, '0')}:${String(archiveTime.getMinutes()).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`

      changeLogs.push({
        time: archiveTimeStr,
        type: 'archive',
        content: '归档工单',
        operator: respondent,
      })
    }
  }

  // 随机选择是否上传日志
  const isUploadLogsList = ['已反馈', '未反馈']
  const isUploadLogs
    = isUploadLogsList[Math.floor(Math.random() * isUploadLogsList.length)]

  // 生成标题
  const title = problemDescription
  // 返回完整的工单数据
  return {
    startTime,
    enterTime,
    responseTime,
    endTime: stage === '已处理' || stage === '已归档' ? endTime : '',
    ticketID,
    stage,
    owner: owners[Math.floor(Math.random() * owners.length)],
    businessOwner:
      businessOwners[Math.floor(Math.random() * businessOwners.length)],
    devOwner: devOwners[Math.floor(Math.random() * devOwners.length)],
    clientIOSOwner:
      osType[0] === 'iOS'
        ? [['iOS开发A'], ['iOS开发B']][Math.floor(Math.random() * 2)]
        : [],
    clientAndroidOwner:
      osType[0] === 'Android'
        ? [['Android开发A'], ['Android开发B']][Math.floor(Math.random() * 2)]
        : [],
    feishuGroup: `【问题处理】${functionType[0]}相关`,
    feishuGroupId: `https://applink.feishu.cn/client/chat/chatter/add_by_link?link_token=${Math.random().toString(36).slice(2, 15)}`,
    problemDescription,
    userTtid,
    attachments,
    apps,
    appVersion,
    osType,
    mobileType,
    hasFeedback: '',
    feedbackPerson: owners[Math.floor(Math.random() * owners.length)][0],
    hasWorkingHours: '',
    severityLevel,
    functionType,
    firstLevelCategory: functionType[0],
    secondLevelCategory: functionType.length > 1 ? functionType[1] : '',
    thirdLevelCategory: '',
    reason: '',
    respondent,
    devProcessor,
    cause,
    result,
    hasClientRelease,
    endType,
    changeLogs,
    isUploadLogs,
    // 以下是Ticket接口所需的其他必填字段
    id: ticketID,
    title,
    text: problemDescription,
    date: enterTime,
    status: stage,
    labels: functionType,
    creator: creators[Math.floor(Math.random() * creators.length)],
    handler: devProcessor,
    createdAt: enterTime,
  }
}

// 只生成一次随机工单数据，避免每次import都变化
let randomTickets: Ticket[] | undefined = (globalThis as any).__RANDOM_TICKETS__
if (!randomTickets) {
  randomTickets = Array.from({ length: 10 }, (_, i) => generateRandomTicket(i + 1))
  ;(globalThis as any).__RANDOM_TICKETS__ = randomTickets
}

export const tickets: Ticket[] = [
  {
    startTime: '2025-05-10 17:50',
    enterTime: '2025-05-13 16:21',
    responseTime: '2025-05-13 18:31',
    endTime: '2025-05-13 18:41',
    ticketID: '1747124494001729',
    stage: '已归档',
    owner: ['陈婷'],
    businessOwner: ['刘智敏'],
    devOwner: ['詹明俊', '陈伟贤'],
    clientIOSOwner: [],
    clientAndroidOwner: [],
    feishuGroup: '【问题处理】业务基础相关',
    feishuGroupId:
      'https://applink.feishu.cn/client/chat/chatter/add_by_link?link_token=a9dle16c-63eb-4119-90c9-7280bc806d02',
    problemDescription:
      '进k歌房听不到音乐和别人唱歌的声音，切换欢游 、麦可、TT 都没声音',
    userTtid: ['240887898'],
    attachments: [
      {
        uid: '6cba2117481844a899d77e0f4c678888.mp4',
        url: 'https://obs-prod-hw-bj-yw-fmp-backend.obs.cn-north-4.myhuaweicloud.com/6cba2117481844a899d77e0f4c678888.mp4',
        name: '飞书20250513-161902.mp4',
        type: 'mp4',
      },
    ],
    apps: ['TT语音'],
    appVersion: ['Version 6.65.3 18735'],
    osType: ['Android'],
    mobileType: ['荣耀畅玩40plus'],
    hasFeedback: '',
    feedbackPerson: '陈婷',
    hasWorkingHours: '',
    severityLevel: '个例问题',
    functionType: ['房间玩法', '房间音频'],
    firstLevelCategory: '房间玩法',
    secondLevelCategory: '房间音频',
    thirdLevelCategory: '',
    reason: '',
    respondent: '刘智敏',
    devProcessor: '卢安',
    cause: '无日志，无法定位',
    result:
      '1. 建议引导用户发现问题时，马上发反馈，方便获取当天日志\n2. 建议引导检查麦克风是否正常，音量是否正常，是否处于静音状态',
    hasClientRelease: '否',
    endType: ['客户端'],
    changeLogs: [
      {
        time: '2025-05-13 16:21:34',
        type: 'create',
        content: '创建了工单',
        operator: '陈婷',
      },
      {
        time: '2025-05-13 18:31:36',
        type: 'appoint',
        content: '指派了工单',
        assignee: '梁伟健',
        assignor: '刘智敏',
      },
      {
        time: '2025-05-13 18:32:37',
        type: 'appoint',
        content: '指派了工单',
        assignee: '卢安',
        assignor: '梁伟健',
      },
      {
        time: '2025-05-13 18:41:34',
        type: 'resolved',
        content: '处理完成工单',
        operator: '卢安',
      },
      {
        time: '2025-05-14 10:15:22',
        type: 'archive',
        content: '归档工单',
        operator: '刘智敏',
      },
    ],
    isUploadLogs: '已反馈',
    // 以下是Ticket接口所需的其他必填字段
    id: '1747124494001729',
    title: '进k歌房听不到声音',
    text: '进k歌房听不到音乐和别人唱歌的声音，切换欢游 、麦可、TT 都没声音',
    date: '2025-05-13 16:21',
    status: '已归档',
    labels: ['房间玩法', '房间音频'],
    creator: 'admin',
    handler: 'admin',
    createdAt: '2025-05-13 16:21',
  },
  // ...只生成一次的随机工单数据...
  ...randomTickets,
]
