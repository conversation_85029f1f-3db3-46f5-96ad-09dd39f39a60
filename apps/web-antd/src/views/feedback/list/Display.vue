<script lang="ts" setup>
import type { Ticket } from './data/tickets'

import { Badge } from '@/components/ui/badge'

import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import {
  Archive,
  ArrowLeft,
  Calendar,
  Clock,
  Forward,
  MoreVertical,
  Paperclip,
  Reply,
  Tag,
  Trash2,
  User,
} from 'lucide-vue-next'

import { ref, watch } from 'vue'

import EditableField from './components/EditableField.vue'

interface TicketDisplayProps {
  ticket: Ticket | undefined
}

const props = defineProps<TicketDisplayProps>()
const emit = defineEmits(['close'])

const ticket = ref<Ticket | undefined>(props.ticket)

// 监听props.ticket的变化，更新本地ticket
watch(
  () => props.ticket,
  (newTicket) => {
    ticket.value = newTicket
  },
  { immediate: true },
)

// 更新工单字段
function updateTicketField(field: keyof Ticket, value: any) {
  if (!ticket.value)
    return

  // 创建一个新的工单对象，以保持响应性
  ticket.value = {
    ...ticket.value,
    [field]: value,
  }

  // 这里可以添加保存到后端的逻辑
  // 在实际应用中，这里应该调用API保存更改
}
</script>

<template>
  <div>
    <div class="flex items-center p-2">
      <div class="flex items-center gap-2">
        <Tooltip>
          <TooltipTrigger as-child>
            <Button
              variant="ghost"
              size="icon"
              :disabled="!ticket"
              @click="emit('close')"
            >
              <ArrowLeft class="size-4" />
              <span class="sr-only">返回</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>返回</TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger as-child>
            <Button variant="ghost" size="icon" :disabled="!ticket">
              <Archive class="size-4" />
              <span class="sr-only">归档</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>归档</TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger as-child>
            <Button variant="ghost" size="icon" :disabled="!ticket">
              <Trash2 class="size-4" />
              <span class="sr-only">删除</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>删除</TooltipContent>
        </Tooltip>
      </div>
      <div class="ml-auto flex items-center gap-2">
        <Tooltip>
          <TooltipTrigger as-child>
            <Button variant="ghost" size="icon" :disabled="!ticket">
              <Reply class="size-4" />
              <span class="sr-only">回复</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>回复</TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger as-child>
            <Button variant="ghost" size="icon" :disabled="!ticket">
              <Forward class="size-4" />
              <span class="sr-only">转发</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>转发</TooltipContent>
        </Tooltip>
      </div>
      <Separator orientation="vertical" class="mx-2 h-6" />
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button variant="ghost" size="icon" :disabled="!ticket">
            <MoreVertical class="size-4" />
            <span class="sr-only">更多</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem>标记为未读</DropdownMenuItem>
          <DropdownMenuItem>添加标签</DropdownMenuItem>
          <DropdownMenuItem>静音工单</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
    <Separator />

    <ScrollArea v-if="ticket" class="h-[calc(100dvh-130px-3rem)]">
      <div class="flex flex-col p-2">
        <!-- 工单详情卡片 -->
        <div class="rounded-lg">
          <!-- 标题和状态区域 -->
          <div class="border-b border-border/20 px-6 py-5">
            <!-- 标题和状态 -->
            <div class="flex items-center gap-3">
              <!-- 状态标签 -->
              <div
                class="flex items-center gap-1.5 whitespace-nowrap rounded-md px-2 py-1 text-xs font-medium"
                :class="[
                  (ticket.stage || ticket.status) === '待处理'
                    ? 'bg-[rgb(215,55,55)]/10 text-[rgb(215,55,55)]'
                    : (ticket.stage || ticket.status) === '处理中'
                      ? 'bg-[rgb(201,134,0)]/10 text-[rgb(201,134,0)]'
                      : (ticket.stage || ticket.status) === '已处理'
                        ? 'bg-[rgb(40,167,69)]/10 text-[rgb(40,167,69)]'
                        : (ticket.stage || ticket.status) === '已归档'
                          ? 'bg-[rgb(100,100,105)]/10 text-[rgb(100,100,105)]'
                          : 'bg-[rgb(0,122,255)]/10 text-[rgb(0,122,255)]',
                ]"
              >
                <div
                  class="size-2 shrink-0 rounded-full"
                  :class="[
                    (ticket.stage || ticket.status) === '待处理'
                      ? 'bg-[rgb(215,55,55)]'
                      : (ticket.stage || ticket.status) === '处理中'
                        ? 'bg-[rgb(201,134,0)]'
                        : (ticket.stage || ticket.status) === '已处理'
                          ? 'bg-[rgb(40,167,69)]'
                          : (ticket.stage || ticket.status) === '已归档'
                            ? 'bg-[rgb(100,100,105)]'
                            : 'bg-[rgb(0,122,255)]',
                  ]"
                />
                {{ ticket.stage || ticket.status }}
              </div>

              <!-- 问题描述作为标题 -->
              <h2 class="line-clamp-2 text-xl font-medium tracking-tight">
                <EditableField
                  v-if="ticket"
                  field-name="problemDescription"
                  field-label="问题描述"
                  :field-value="
                    ticket.problemDescription || ticket.text || ticket.title
                  "
                  field-type="textarea"
                  @update:field-value="
                    updateTicketField('problemDescription', $event)
                  "
                />
              </h2>
            </div>

            <!-- 工单ID和创建时间 -->
            <div
              class="mt-2 flex items-center gap-3 text-xs text-muted-foreground"
            >
              <span>TicketID#{{ ticket.ticketID || ticket.id }}</span>
              <span class="text-muted-foreground/40">•</span>
              <span>创建于:
                {{ ticket.enterTime || ticket.createdAt || ticket.date }}</span>
            </div>

            <!-- 工单标签 -->
            <div
              v-if="ticket.labels || ticket.functionType"
              class="mt-3 flex flex-wrap gap-2"
            >
              <Badge
                v-for="(label, index) of ticket.labels
                  || ticket.functionType
                  || []"
                :key="`${label}-${index}`"
                variant="outline"
                class="flex items-center gap-1 border-0 rounded-full bg-secondary/5 px-2 py-0.5 text-xs font-normal"
              >
                <Tag class="size-2.5 text-primary/70" />
                {{ label }}
              </Badge>
            </div>
          </div>

          <!-- 工单内容区域 -->
          <div class="p-6">
            <!-- 工单信息 -->
            <div class="grid grid-cols-2 mb-6 gap-x-6 gap-y-4 text-sm">
              <!-- 基本信息 -->
              <div
                class="flex items-center gap-2 border-b border-border/10 pb-2"
              >
                <div class="w-16 text-xs text-muted-foreground">
                  创建人
                </div>
                <div class="flex items-center gap-1.5">
                  <User class="size-3 text-primary/70" />
                  <EditableField
                    v-if="ticket"
                    field-name="creator"
                    field-label="创建人"
                    :field-value="ticket.creator || '未知'"
                    @update:field-value="updateTicketField('creator', $event)"
                  />
                </div>
              </div>
              <div
                class="flex items-center gap-2 border-b border-border/10 pb-2"
              >
                <div class="w-16 text-xs text-muted-foreground">
                  处理人
                </div>
                <div class="flex items-center gap-1.5">
                  <User class="size-3 text-primary/70" />
                  <EditableField
                    v-if="ticket"
                    field-name="handler"
                    field-label="处理人"
                    :field-value="
                      ticket.handler || ticket.devProcessor || '未分配'
                    "
                    @update:field-value="updateTicketField('handler', $event)"
                  />
                </div>
              </div>

              <!-- 截止日期 -->
              <div
                class="flex items-center gap-2 border-b border-border/10 pb-2"
              >
                <div class="w-16 text-xs text-muted-foreground">
                  截止日期
                </div>
                <div class="flex items-center gap-1.5">
                  <Calendar class="size-3 text-primary/70" />
                  <EditableField
                    v-if="ticket"
                    field-name="dueDate"
                    field-label="截止日期"
                    :field-value="ticket.dueDate"
                    field-type="date"
                    @update:field-value="updateTicketField('dueDate', $event)"
                  />
                </div>
              </div>
              <div
                class="flex items-center gap-2 border-b border-border/10 pb-2"
              >
                <div class="w-16 text-xs text-muted-foreground">
                  严重程度
                </div>
                <EditableField
                  v-if="ticket"
                  field-name="severityLevel"
                  field-label="严重程度"
                  :field-value="ticket.severityLevel"
                  field-type="select"
                  :field-options="[
                    { label: '低', value: '低' },
                    { label: '中', value: '中' },
                    { label: '高', value: '高' },
                    { label: '紧急', value: '紧急' },
                  ]"
                  @update:field-value="
                    updateTicketField('severityLevel', $event)
                  "
                />
              </div>

              <!-- 时间信息 -->
              <div
                class="flex items-center gap-2 border-b border-border/10 pb-2"
              >
                <div class="w-16 text-xs text-muted-foreground">
                  创建时间
                </div>
                <div class="flex items-center gap-1.5">
                  <Clock class="size-3 text-primary/70" />
                  <span>{{
                    ticket.createdAt || ticket.enterTime || ticket.date
                  }}</span>
                </div>
              </div>
              <div
                class="flex items-center gap-2 border-b border-border/10 pb-2"
              >
                <div class="w-16 text-xs text-muted-foreground">
                  处理时间
                </div>
                <div class="flex items-center gap-1.5">
                  <Clock class="size-3 text-primary/70" />
                  <span>{{
                    ticket.endTime || ticket.updatedAt || '未处理'
                  }}</span>
                </div>
              </div>

              <!-- 设备信息 -->
              <div
                v-if="ticket.apps && ticket.apps.length > 0"
                class="flex items-center gap-2 border-b border-border/10 pb-2"
              >
                <div class="w-16 text-xs text-muted-foreground">
                  应用
                </div>
                <span>{{ ticket.apps.join(', ') }}</span>
              </div>
              <div
                v-if="ticket.appVersion && ticket.appVersion.length > 0"
                class="flex items-center gap-2 border-b border-border/10 pb-2"
              >
                <div class="w-16 text-xs text-muted-foreground">
                  版本
                </div>
                <span>{{ ticket.appVersion.join(', ') }}</span>
              </div>

              <!-- 设备类型 -->
              <div
                v-if="ticket.osType && ticket.osType.length > 0"
                class="flex items-center gap-2 border-b border-border/10 pb-2"
              >
                <div class="w-16 text-xs text-muted-foreground">
                  系统
                </div>
                <span>{{ ticket.osType.join(', ') }}</span>
              </div>
              <div
                v-if="ticket.mobileType && ticket.mobileType.length > 0"
                class="flex items-center gap-2 border-b border-border/10 pb-2"
              >
                <div class="w-16 text-xs text-muted-foreground">
                  设备
                </div>
                <span>{{ ticket.mobileType.join(', ') }}</span>
              </div>

              <!-- 分类 -->
              <div
                v-if="ticket.firstLevelCategory"
                class="flex items-center gap-2 border-b border-border/10 pb-2"
              >
                <div class="w-16 text-xs text-muted-foreground">
                  问题分类
                </div>
                <span>
                  {{ ticket.firstLevelCategory }} >
                  {{ ticket.secondLevelCategory || '' }}
                </span>
              </div>
            </div>

            <!-- 原因和结果 -->
            <div v-if="ticket.cause || ticket.result" class="mb-6 space-y-4">
              <div v-if="ticket.cause" class="space-y-1">
                <div class="text-xs text-muted-foreground font-medium">
                  问题原因
                </div>
                <div
                  class="border border-border/30 rounded-md p-3 text-sm leading-relaxed"
                >
                  <EditableField
                    v-if="ticket"
                    field-name="cause"
                    field-label="问题原因"
                    :field-value="ticket.cause"
                    field-type="textarea"
                    @update:field-value="updateTicketField('cause', $event)"
                  />
                </div>
              </div>
              <div v-if="ticket.result" class="space-y-1">
                <div class="text-xs text-muted-foreground font-medium">
                  处理结果
                </div>
                <div
                  class="whitespace-pre-line border border-border/30 rounded-md p-3 text-sm leading-relaxed"
                >
                  <EditableField
                    v-if="ticket"
                    field-name="result"
                    field-label="处理结果"
                    :field-value="ticket.result"
                    field-type="textarea"
                    @update:field-value="updateTicketField('result', $event)"
                  />
                </div>
              </div>
            </div>

            <!-- 附件 -->
            <div
              v-if="ticket.attachments && ticket.attachments.length > 0"
              class="mb-6 space-y-3"
            >
              <div class="text-xs text-muted-foreground font-medium">
                附件
              </div>
              <div class="space-y-2">
                <div
                  v-for="(attachment, index) in ticket.attachments"
                  :key="attachment.uid || attachment.name || index"
                  class="flex items-center gap-2 border border-border/30 rounded-md p-2"
                >
                  <div class="flex-1">
                    <div class="text-sm font-medium">
                      {{ attachment.name }}
                    </div>
                    <div class="text-xs text-muted-foreground">
                      {{ attachment.type }}
                      {{ attachment.size }}
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    class="gap-1 border border-border/30 rounded-md px-2 py-1"
                    as="a"
                    :href="attachment.url"
                    target="_blank"
                  >
                    <Paperclip class="size-3 text-primary/70" />
                    查看
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 变更日志 -->
        <div class="p-6">
          <h3 class="mb-3 text-base font-medium">
            处理历史
          </h3>
          <div
            v-if="ticket.changeLogs && ticket.changeLogs.length > 0"
            class="rounded-lg bg-background p-3"
          >
            <div class="relative">
              <!-- 时间线 -->
              <div
                class="absolute bottom-0 left-0 top-0 w-px bg-primary/20"
              />

              <!-- 日志项 -->
              <div
                v-for="(log, index) in ticket.changeLogs"
                :key="`${log.time}-${log.type}-${index}`"
                class="mb-5 last:mb-0"
              >
                <div class="relative flex items-start">
                  <div
                    class="absolute top-1.5 h-2.5 w-2.5 rounded-full bg-primary/80 shadow-sm -left-[5px]"
                  />
                  <div class="ml-4">
                    <p class="text-sm leading-relaxed">
                      {{ log.content }}
                    </p>
                    <div
                      class="flex items-center gap-1.5 text-xs text-muted-foreground"
                    >
                      <span>{{ log.time }}</span>
                      <span class="text-muted-foreground/40">•</span>
                      <span>{{ log.operator || log.assignor || '' }}</span>
                      <span v-if="log.assignee">→ {{ log.assignee }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            v-else
            class="border border-border/30 rounded-lg bg-background p-3 text-center text-sm text-muted-foreground shadow-md"
          >
            暂无处理记录
          </div>
        </div>
      </div>
    </ScrollArea>
    <div v-else>
      <div class="h-full flex flex-col items-center justify-center">
        <div class="text-muted-foreground font-medium">
          暂无数据
        </div>
      </div>
    </div>
  </div>
</template>
