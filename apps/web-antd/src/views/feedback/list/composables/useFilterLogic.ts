import { computed, ref } from 'vue';

// 定义筛选选项类型
export interface FilterOption {
  label: string;
  value: string;
  icon?: any;
  color?: string;
  count?: number;
}

// 定义筛选类别类型
export interface FilterCategory {
  label: string;
  key: string;
  icon?: any;
  options?: FilterOption[];
  hasSubmenu?: boolean;
}

/**
 * 筛选逻辑的Composable函数
 * @param initialFilters 初始筛选条件
 * @param onFilterChange 筛选条件变化时的回调函数
 * @returns 筛选相关的状态和方法
 */
export function useFilterLogic(
  initialFilters: Record<string, string[]> = {},
  onFilterChange?: (filters: Record<string, string[]>) => void,
) {
  // 下拉菜单是否打开
  const isOpen = ref(false);

  // 搜索关键字
  const searchKeyword = ref('');

  // 选中的筛选项
  const selectedFilters = ref<Record<string, string[]>>(
    JSON.parse(JSON.stringify(initialFilters || {})),
  );

  // 获取选中的筛选项数量
  const selectedCount = computed(() => {
    let count = 0;
    for (const key in selectedFilters.value) {
      count += selectedFilters.value[key]?.length || 0;
    }
    return count;
  });

  /**
   * 切换筛选选项
   * @param categoryKey 类别键
   * @param optionValue 选项值
   * @param event 事件对象
   * @param isMultiSelect 是否为多选模式
   */
  function toggleFilter(
    categoryKey: string,
    optionValue: string,
    event?: Event | MouseEvent,
    isMultiSelect = false,
  ) {
    // 阻止事件冒泡，防止关闭下拉菜单
    if (event) {
      event.stopPropagation();
    }

    // 打印调试信息
    // eslint-disable-next-line no-console
    console.log(
      'toggleFilter START:',
      categoryKey,
      optionValue,
      isMultiSelect,
      [...(selectedFilters.value[categoryKey] || [])],
    );

    // 确保该类别有对应的数组
    if (!selectedFilters.value[categoryKey]) {
      selectedFilters.value[categoryKey] = [];
    }

    // 检查值是否已经在数组中
    const index = selectedFilters.value[categoryKey].indexOf(optionValue);

    if (index === -1) {
      // 值不在数组中，添加它
      // 如果不是多选模式，先清空之前的选择
      if (!isMultiSelect) {
        selectedFilters.value[categoryKey] = [];
      }
      selectedFilters.value[categoryKey].push(optionValue);
      // eslint-disable-next-line no-console
      console.log('添加筛选值:', categoryKey, optionValue);
    } else {
      // 值已经在数组中，移除它（多选模式）
      if (isMultiSelect) {
        selectedFilters.value[categoryKey].splice(index, 1);
        // eslint-disable-next-line no-console
        console.log('移除筛选值:', categoryKey, optionValue);
      } else {
        // 单选模式下，如果已选中，则不做任何操作
        return;
      }
    }

    // 调用回调函数
    if (onFilterChange) {
      onFilterChange(selectedFilters.value);
    }

    // 打印调试信息
    // eslint-disable-next-line no-console
    console.log(
      'toggleFilter END:',
      categoryKey,
      optionValue,
      selectedFilters.value,
    );

    // 如果不是多选模式，则关闭菜单
    if (!isMultiSelect) {
      isOpen.value = false;
    }
  }

  /**
   * 检查选项是否被选中
   * @param categoryKey 类别键
   * @param optionValue 选项值
   * @returns 是否被选中
   */
  function isOptionSelected(categoryKey: string, optionValue: string): boolean {
    return selectedFilters.value[categoryKey]?.includes(optionValue) || false;
  }

  /**
   * 筛选选项
   * @param options 选项数组
   * @returns 筛选后的选项数组
   */
  function filterOptions(options: FilterOption[] = []): FilterOption[] {
    if (!searchKeyword.value) return options;

    return options.filter((option) =>
      option.label.toLowerCase().includes(searchKeyword.value.toLowerCase()),
    );
  }

  /**
   * 筛选类别
   * @param categories 类别数组
   * @returns 筛选后的类别数组
   */
  function filterCategories(
    categories: FilterCategory[] = [],
  ): FilterCategory[] {
    if (!searchKeyword.value) return categories;

    return categories.filter((category) =>
      category.label.toLowerCase().includes(searchKeyword.value.toLowerCase()),
    );
  }

  /**
   * 更新筛选条件
   * @param filters 新的筛选条件
   */
  function updateFilters(filters: Record<string, string[]>) {
    selectedFilters.value = JSON.parse(JSON.stringify(filters || {}));

    // 调用回调函数
    if (onFilterChange) {
      onFilterChange(selectedFilters.value);
    }
  }

  /**
   * 静默更新筛选条件（不触发回调）
   * @param filters 新的筛选条件
   */
  function updateFiltersSilently(filters: Record<string, string[]>) {
    selectedFilters.value = JSON.parse(JSON.stringify(filters || {}));
  }

  /**
   * 清除特定类别的筛选条件
   * @param categoryKey 类别键
   */
  function clearCategoryFilters(categoryKey: string) {
    if (selectedFilters.value[categoryKey]) {
      // 完全删除这个键，而不是设置为空数组
      delete selectedFilters.value[categoryKey];

      // 调用回调函数
      if (onFilterChange) {
        onFilterChange(selectedFilters.value);
      }
    }
  }

  /**
   * 清除所有筛选条件
   */
  function clearAllFilters() {
    // 创建一个全新的空对象
    selectedFilters.value = {};

    // 调用回调函数
    if (onFilterChange) {
      onFilterChange(selectedFilters.value);
    }

    // 打印调试信息
    // eslint-disable-next-line no-console
    console.log('清除所有筛选条件:', JSON.stringify(selectedFilters.value));
  }

  return {
    isOpen,
    searchKeyword,
    selectedFilters,
    selectedCount,
    toggleFilter,
    isOptionSelected,
    filterOptions,
    filterCategories,
    updateFilters,
    updateFiltersSilently,
    clearCategoryFilters,
    clearAllFilters,
  };
}
