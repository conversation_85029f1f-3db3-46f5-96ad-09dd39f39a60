import type { Ticket } from '../types/ticket';

/**
 * 模拟工单数据
 * 这里只保留一些示例数据，实际数据应该从API获取
 */
export const mockTickets: Ticket[] = [
  {
    id: '1747124494001729',
    ticketID: '1747124494001729',
    title: '进k歌房听不到声音',
    text: '进k歌房听不到音乐和别人唱歌的声音，切换欢游 、麦可、TT 都没声音',
    problemDescription: '进k歌房听不到音乐和别人唱歌的声音，切换欢游 、麦可、TT 都没声音',
    date: '2025-05-13 16:21',
    status: '已归档',
    stage: '已归档',
    labels: ['房间玩法', '房间音频'],
    
    // 时间信息
    startTime: '2025-05-10 17:50',
    enterTime: '2025-05-13 16:21',
    responseTime: '2025-05-13 18:31',
    endTime: '2025-05-13 18:41',
    createdAt: '2025-05-13 16:21',
    
    // 人员信息
    creator: 'admin',
    handler: '卢安',
    owner: ['陈婷'],
    businessOwner: ['刘智敏'],
    devOwner: ['詹明俊', '陈伟贤'],
    respondent: '刘智敏',
    devProcessor: '卢安',
    feedbackPerson: '陈婷',
    
    // 分类信息
    severityLevel: '个例问题',
    functionType: ['房间玩法', '房间音频'],
    firstLevelCategory: '房间玩法',
    secondLevelCategory: '房间音频',
    endType: ['客户端'],
    
    // 设备信息
    apps: ['TT语音'],
    appVersion: ['Version 6.65.3 18735'],
    osType: ['Android'],
    mobileType: ['荣耀畅玩40plus'],
    userTtid: ['240887898'],
    
    // 处理信息
    feishuGroup: '【问题处理】业务基础相关',
    feishuGroupId: 'https://applink.feishu.cn/client/chat/chatter/add_by_link?link_token=a9dle16c-63eb-4119-90c9-7280bc806d02',
    cause: '无日志，无法定位',
    result: '1. 建议引导用户发现问题时，马上发反馈，方便获取当天日志\n2. 建议引导检查麦克风是否正常，音量是否正常，是否处于静音状态',
    hasClientRelease: '否',
    isUploadLogs: '已反馈',
    
    // 附件
    attachments: [
      {
        uid: '6cba2117481844a899d77e0f4c678888.mp4',
        url: 'https://obs-prod-hw-bj-yw-fmp-backend.obs.cn-north-4.myhuaweicloud.com/6cba2117481844a899d77e0f4c678888.mp4',
        name: '飞书20250513-161902.mp4',
        type: 'mp4',
      },
    ],
    
    // 变更日志
    changeLogs: [
      {
        time: '2025-05-13 16:21:34',
        type: 'create',
        content: '创建了工单',
        operator: '陈婷',
      },
      {
        time: '2025-05-13 18:31:36',
        type: 'appoint',
        content: '指派了工单',
        assignee: '梁伟健',
        assignor: '刘智敏',
      },
      {
        time: '2025-05-13 18:32:37',
        type: 'appoint',
        content: '指派了工单',
        assignee: '卢安',
        assignor: '梁伟健',
      },
      {
        time: '2025-05-13 18:41:34',
        type: 'resolved',
        content: '处理完成工单',
        operator: '卢安',
      },
      {
        time: '2025-05-14 10:15:22',
        type: 'archive',
        content: '归档工单',
        operator: '刘智敏',
      },
    ],
  },
  // 可以添加更多示例数据...
];

/**
 * 生成随机工单数据的函数
 * @param count 生成数量
 * @returns 工单数组
 */
export function generateMockTickets(count: number = 10): Ticket[] {
  const statuses = ['待处理', '处理中', '已处理', '已归档'];
  const creators = ['admin', '张伟', '李娜', '王磊', '赵芳'];
  const handlers = ['卢安', '张开发', '王工程师', '李技术'];
  const problems = [
    '语音房间内麦克风无法使用',
    '直播间礼物特效不显示',
    '游戏房间内延迟严重',
    '语音通话突然中断',
    '房间内背景音乐无法切换',
    '进入语音房间后自动退出',
    '房间内其他用户头像不显示',
    '语音房间内无法调节音量',
    '创建房间失败，提示网络异常',
    '进k歌房听不到音乐和别人唱歌的声音',
  ];
  
  const tickets: Ticket[] = [];
  
  for (let i = 0; i < count; i++) {
    const ticketId = `${Date.now()}${i.toString().padStart(3, '0')}`;
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const creator = creators[Math.floor(Math.random() * creators.length)];
    const handler = handlers[Math.floor(Math.random() * handlers.length)];
    const problem = problems[Math.floor(Math.random() * problems.length)];
    
    // 生成随机时间
    const now = new Date();
    const createdAt = new Date(now.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000);
    const enterTime = createdAt.toISOString().slice(0, 16).replace('T', ' ');
    
    tickets.push({
      id: ticketId,
      ticketID: ticketId,
      title: problem,
      text: problem,
      problemDescription: problem,
      date: enterTime,
      status,
      stage: status,
      labels: ['功能问题', '用户反馈'],
      
      // 时间信息
      enterTime,
      createdAt: enterTime,
      
      // 人员信息
      creator,
      handler,
      
      // 分类信息
      severityLevel: '个例问题',
      functionType: ['房间玩法'],
      
      // 设备信息
      apps: ['TT语音'],
      osType: ['Android'],
    });
  }
  
  return tickets;
}
