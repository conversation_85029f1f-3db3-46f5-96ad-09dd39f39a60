export interface Ticket {
  // 基本信息
  id: string
  ticketID?: string // 工单ID（API返回的主要ID字段）
  title?: string // 工单标题（可选，因为API可能不返回）
  text?: string // 工单描述（可选）
  problemDescription?: string // 问题描述（API主要字段）
  date?: string // 日期（可选）
  status?: string // 状态（前端使用）
  stage?: string // 工单阶段（API返回的主要状态字段）
  labels?: string[] // 标签（可选）

  // 时间信息
  startTime?: string // 开始时间
  enterTime?: string // 进入时间
  responseTime?: string // 响应时间
  endTime?: string // 结束时间
  createdAt?: string // 创建时间（可选）
  updatedAt?: string
  dueDate?: string

  // 人员信息
  creator?: string // 创建人（可选）
  handler?: string // 处理人（可选）
  owner?: string[] // 负责人
  businessOwner?: string[] // 业务负责人
  devOwner?: string[] // 开发负责人
  clientIOSOwner?: string[] // iOS客户端负责人
  clientAndroidOwner?: string[] // Android客户端负责人
  respondent?: string // 响应人
  devProcessor?: string // 开发处理人
  feedbackPerson?: string // 反馈人

  // 分类信息
  severityLevel?: string // 严重程度
  functionType?: string[] // 功能类型
  firstLevelCategory?: string // 一级分类
  secondLevelCategory?: string // 二级分类
  thirdLevelCategory?: string // 三级分类
  endType?: string[] // 结束类型

  // 设备信息
  apps?: string[] // 应用
  appVersion?: string[] // 应用版本
  osType?: string[] // 操作系统类型
  mobileType?: string[] // 手机型号
  userTtid?: string[] // 用户TTID

  // 处理信息
  feishuGroup?: string // 飞书群组
  feishuGroupId?: string // 飞书群组ID
  cause?: string // 原因
  result?: string // 结果
  reason?: string // 理由
  hasFeedback?: string // 是否有反馈
  hasWorkingHours?: string // 是否有工时
  hasClientRelease?: string // 是否有客户端发布
  isUploadLogs?: string // 是否上传日志

  // 附件和评论
  attachments?: {
    name: string
    size?: string
    type: string
    uid?: string
    url: string
  }[]

  comments?: {
    creator: string
    date: string
    id: string
    text: string
  }[]

  // 变更日志
  changeLogs?: {
    assignee?: string
    assignor?: string
    content: string
    operator?: string
    time: string
    type: string
  }[]
}

// 工单筛选相关类型
export interface FilterOption {
  label: string
  value: string
  count?: number
}

export interface FilterCategory {
  key: string
  label: string
  options: FilterOption[]
}

// 工单列表相关类型
export interface TicketListProps {
  items?: Ticket[]
  selectedTicket?: null | string
  searchValue?: string
}

// 工单详情相关类型
export interface TicketDisplayProps {
  ticket?: Ticket | null
}
