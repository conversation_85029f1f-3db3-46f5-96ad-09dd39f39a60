<!-- 从list/components移动过来的EditableField.vue -->
<script lang="ts" setup>
import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
} from '@vben-core/shadcn-ui';

import { Check, Edit, X } from 'lucide-vue-next';
import { computed, ref, watch } from 'vue';

interface EditableFieldProps {
  label: string;
  value: string;
  type?: 'text' | 'textarea' | 'select';
  options?: string[];
  placeholder?: string;
  disabled?: boolean;
}

const props = withDefaults(defineProps<EditableFieldProps>(), {
  type: 'text',
  options: () => [],
  placeholder: '',
  disabled: false,
});

const emit = defineEmits<{
  (e: 'update', value: string): void;
}>();

// 编辑状态
const isEditing = ref(false);
const editValue = ref(props.value);

// 监听props.value变化
watch(
  () => props.value,
  (newValue) => {
    editValue.value = newValue;
  },
);

// 显示值
const displayValue = computed(() => {
  return props.value || '未设置';
});

// 开始编辑
function startEdit() {
  if (props.disabled) return;
  isEditing.value = true;
  editValue.value = props.value;
}

// 保存编辑
function saveEdit() {
  emit('update', editValue.value);
  isEditing.value = false;
}

// 取消编辑
function cancelEdit() {
  editValue.value = props.value;
  isEditing.value = false;
}

// 处理键盘事件
function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    saveEdit();
  } else if (event.key === 'Escape') {
    event.preventDefault();
    cancelEdit();
  }
}
</script>

<template>
  <div class="space-y-2">
    <label class="text-sm font-medium">{{ label }}</label>
    
    <!-- 非编辑状态 -->
    <div
      v-if="!isEditing"
      class="group flex items-center justify-between rounded-md border border-transparent p-2 hover:border-border hover:bg-accent/50 transition-colors"
      :class="{ 'cursor-pointer': !disabled }"
      @click="startEdit"
    >
      <span class="text-sm" :class="{ 'text-muted-foreground': !value }">
        {{ displayValue }}
      </span>
      <Edit
        v-if="!disabled"
        class="h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity"
      />
    </div>
    
    <!-- 编辑状态 -->
    <div v-else class="space-y-2">
      <!-- 文本输入 -->
      <Input
        v-if="type === 'text'"
        v-model="editValue"
        :placeholder="placeholder"
        class="text-sm"
        @keydown="handleKeydown"
      />
      
      <!-- 多行文本输入 -->
      <Textarea
        v-else-if="type === 'textarea'"
        v-model="editValue"
        :placeholder="placeholder"
        class="text-sm min-h-[80px]"
        @keydown="handleKeydown"
      />
      
      <!-- 选择器 -->
      <Select v-else-if="type === 'select'" v-model="editValue">
        <SelectTrigger class="text-sm">
          <SelectValue :placeholder="placeholder" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem
            v-for="option in options"
            :key="option"
            :value="option"
          >
            {{ option }}
          </SelectItem>
        </SelectContent>
      </Select>
      
      <!-- 操作按钮 -->
      <div class="flex items-center gap-2">
        <Button size="sm" @click="saveEdit">
          <Check class="h-3 w-3 mr-1" />
          保存
        </Button>
        <Button variant="outline" size="sm" @click="cancelEdit">
          <X class="h-3 w-3 mr-1" />
          取消
        </Button>
      </div>
    </div>
  </div>
</template>
