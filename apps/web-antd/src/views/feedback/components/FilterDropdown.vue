<!-- 从list/components移动过来的FilterDropdown.vue -->
<script lang="ts" setup>
import type { FilterCategory } from '../types/ticket';

import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
  Input,
} from '@vben-core/shadcn-ui';

import { ChevronDown, Filter, Search } from 'lucide-vue-next';
import { computed, ref, watch } from 'vue';

import { useFilterLogic } from '../composables/useFilterLogic';
import FilterOptionsList from './FilterOptionsList.vue';

interface FilterDropdownProps {
  categories: FilterCategory[];
  initialFilters?: Record<string, string[]>;
}

const props = withDefaults(defineProps<FilterDropdownProps>(), {
  initialFilters: () => ({}),
});

const emit = defineEmits<{
  (e: 'filter', filters: Record<string, string[]>): void;
}>();

// 使用筛选逻辑
const {
  isOpen,
  searchKeyword,
  selectedFilters,
  selectedCount,
  toggleFilter,
  filterCategories,
  updateFilters,
} = useFilterLogic(props.initialFilters, (filters) => {
  emit('filter', filters);
});

// 监听props变化
watch(
  () => props.initialFilters,
  (newFilters) => {
    updateFilters(newFilters);
  },
  { deep: true, immediate: true },
);

// 筛选后的类别
const filteredCategories = computed(() => {
  return filterCategories(props.categories);
});

// 处理选项切换
function handleOptionToggle(
  categoryKey: string,
  optionValue: string,
  event: Event,
  isMultiSelect = true,
) {
  toggleFilter(categoryKey, optionValue, event, isMultiSelect);
}

// 检查选项是否被选中
function isOptionSelected(categoryKey: string, optionValue: string): boolean {
  return selectedFilters.value[categoryKey]?.includes(optionValue) || false;
}
</script>

<template>
  <DropdownMenu v-model:open="isOpen">
    <DropdownMenuTrigger as-child>
      <Button variant="outline" size="sm" class="h-8 gap-1">
        <Filter class="h-3.5 w-3.5" />
        <span>筛选</span>
        <span
          v-if="selectedCount > 0"
          class="ml-1 rounded bg-primary px-1.5 py-0.5 text-xs text-primary-foreground"
        >
          {{ selectedCount }}
        </span>
        <ChevronDown class="h-3.5 w-3.5" />
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent class="w-[300px]" align="start">
      <!-- 搜索框 -->
      <div class="p-2">
        <div class="relative">
          <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            v-model="searchKeyword"
            placeholder="搜索筛选条件..."
            class="pl-8"
          />
        </div>
      </div>

      <!-- 筛选类别 -->
      <DropdownMenuGroup>
        <DropdownMenuSub
          v-for="category in filteredCategories"
          :key="category.key"
        >
          <DropdownMenuSubTrigger class="flex cursor-pointer items-center">
            <span>{{ category.label }}</span>
            <span
              v-if="selectedFilters[category.key]?.length"
              class="ml-auto rounded bg-primary px-1.5 py-0.5 text-xs text-primary-foreground"
            >
              {{ selectedFilters[category.key].length }}
            </span>
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent class="w-48">
            <FilterOptionsList
              :category-key="category.key"
              :options="category.options || []"
              :selected-values="selectedFilters[category.key] || []"
              :search-keyword="searchKeyword"
              @option-toggle="handleOptionToggle"
              @is-option-selected="isOptionSelected"
            />
          </DropdownMenuSubContent>
        </DropdownMenuSub>
      </DropdownMenuGroup>
    </DropdownMenuContent>
  </DropdownMenu>
</template>
