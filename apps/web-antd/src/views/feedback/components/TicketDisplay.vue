<!-- 重命名后的Display.vue -->
<script lang="ts" setup>
import type { Ticket } from '../types/ticket';

import {
  Badge,
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  ScrollArea,
  Separator,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@vben-core/shadcn-ui';

import { formatDistanceToNow } from 'date-fns';
import {
  Archive,
  ArchiveX,
  Clock,
  Forward,
  MoreVertical,
  Reply,
  ReplyAll,
  Search,
  Trash2,
} from 'lucide-vue-next';
import { computed } from 'vue';

import EditableField from './EditableField.vue';

interface TicketDisplayProps {
  ticket?: Ticket | null;
}

const props = defineProps<TicketDisplayProps>();

const emit = defineEmits<{
  (e: 'toggle-filter'): void;
}>();

// 计算属性
const ticketData = computed(() => props.ticket);

// 格式化时间
function formatTime(timeStr?: string) {
  if (!timeStr) return '未知时间';
  try {
    return formatDistanceToNow(new Date(timeStr), { addSuffix: true });
  } catch {
    return timeStr;
  }
}

// 获取状态颜色
function getStatusColor(status?: string) {
  const colors = {
    待处理: 'destructive',
    处理中: 'secondary',
    已处理: 'default',
    已归档: 'outline',
  };
  return colors[status as keyof typeof colors] || 'default';
}

// 处理操作
function handleAction(action: string) {
  console.warn(`执行操作: ${action}`);
}
</script>

<template>
  <div class="flex h-full flex-col">
    <!-- 工具栏 -->
    <div class="flex items-center p-2">
      <div class="flex items-center gap-2">
        <Tooltip>
          <TooltipTrigger as-child>
            <Button variant="ghost" size="icon" @click="emit('toggle-filter')">
              <Search class="h-4 w-4" />
              <span class="sr-only">搜索</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>搜索</TooltipContent>
        </Tooltip>
        
        <Tooltip>
          <TooltipTrigger as-child>
            <Button variant="ghost" size="icon" @click="handleAction('archive')">
              <Archive class="h-4 w-4" />
              <span class="sr-only">归档</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>归档</TooltipContent>
        </Tooltip>
        
        <Tooltip>
          <TooltipTrigger as-child>
            <Button variant="ghost" size="icon" @click="handleAction('delete')">
              <Trash2 class="h-4 w-4" />
              <span class="sr-only">删除</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>删除</TooltipContent>
        </Tooltip>
      </div>
      
      <div class="ml-auto flex items-center gap-2">
        <Tooltip>
          <TooltipTrigger as-child>
            <Button variant="ghost" size="icon" @click="handleAction('reply')">
              <Reply class="h-4 w-4" />
              <span class="sr-only">回复</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>回复</TooltipContent>
        </Tooltip>
        
        <Tooltip>
          <TooltipTrigger as-child>
            <Button variant="ghost" size="icon" @click="handleAction('replyAll')">
              <ReplyAll class="h-4 w-4" />
              <span class="sr-only">回复全部</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>回复全部</TooltipContent>
        </Tooltip>
        
        <Tooltip>
          <TooltipTrigger as-child>
            <Button variant="ghost" size="icon" @click="handleAction('forward')">
              <Forward class="h-4 w-4" />
              <span class="sr-only">转发</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>转发</TooltipContent>
        </Tooltip>
      </div>
      
      <Separator orientation="vertical" class="mx-2 h-6" />
      
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button variant="ghost" size="icon">
            <MoreVertical class="h-4 w-4" />
            <span class="sr-only">更多</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem @click="handleAction('markAsUnread')">
            标记为未读
          </DropdownMenuItem>
          <DropdownMenuItem @click="handleAction('star')">
            加星标
          </DropdownMenuItem>
          <DropdownMenuItem @click="handleAction('addLabel')">
            添加标签
          </DropdownMenuItem>
          <DropdownMenuItem @click="handleAction('mute')">
            静音
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
    
    <Separator />
    
    <!-- 工单详情内容 -->
    <ScrollArea class="flex-1">
      <div v-if="ticketData" class="flex flex-1 flex-col">
        <div class="flex items-start p-4">
          <div class="flex items-start gap-4 text-sm">
            <div class="grid gap-1">
              <!-- 状态和标题 -->
              <div class="flex items-center gap-2">
                <Badge :variant="getStatusColor(ticketData.stage || ticketData.status)">
                  {{ ticketData.stage || ticketData.status || '未知状态' }}
                </Badge>
                <div class="font-semibold">
                  {{ ticketData.problemDescription || ticketData.title || `工单#${ticketData.ticketID || ticketData.id}` }}
                </div>
              </div>
              
              <!-- 工单ID和时间 -->
              <div class="line-clamp-1 text-xs">
                工单ID: {{ ticketData.ticketID || ticketData.id }}
              </div>
              <div class="line-clamp-1 text-xs">
                <Clock class="mr-1 inline h-3 w-3" />
                创建于 {{ formatTime(ticketData.enterTime || ticketData.createdAt) }}
              </div>
            </div>
          </div>
        </div>
        
        <Separator />
        
        <!-- 可编辑字段区域 -->
        <div class="p-4 space-y-4">
          <!-- 基本信息 -->
          <div class="space-y-3">
            <h3 class="text-sm font-medium">基本信息</h3>
            
            <EditableField
              label="问题描述"
              :value="ticketData.problemDescription || ''"
              type="textarea"
              @update="(value) => console.log('更新问题描述:', value)"
            />
            
            <EditableField
              label="处理人"
              :value="ticketData.handler || ''"
              type="select"
              :options="['张伟', '李娜', '王磊', '赵芳', '陈明']"
              @update="(value) => console.log('更新处理人:', value)"
            />
            
            <EditableField
              label="状态"
              :value="ticketData.stage || ticketData.status || ''"
              type="select"
              :options="['待处理', '处理中', '已处理', '已归档']"
              @update="(value) => console.log('更新状态:', value)"
            />
          </div>
          
          <!-- 标签 -->
          <div v-if="ticketData.labels && ticketData.labels.length > 0" class="space-y-2">
            <h3 class="text-sm font-medium">标签</h3>
            <div class="flex flex-wrap gap-1">
              <Badge v-for="label in ticketData.labels" :key="label" variant="secondary">
                {{ label }}
              </Badge>
            </div>
          </div>
          
          <!-- 时间信息 -->
          <div class="space-y-2">
            <h3 class="text-sm font-medium">时间信息</h3>
            <div class="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
              <div v-if="ticketData.startTime">开始时间: {{ ticketData.startTime }}</div>
              <div v-if="ticketData.enterTime">进入时间: {{ ticketData.enterTime }}</div>
              <div v-if="ticketData.responseTime">响应时间: {{ ticketData.responseTime }}</div>
              <div v-if="ticketData.endTime">结束时间: {{ ticketData.endTime }}</div>
            </div>
          </div>
          
          <!-- 人员信息 -->
          <div class="space-y-2">
            <h3 class="text-sm font-medium">人员信息</h3>
            <div class="grid grid-cols-1 gap-2 text-xs">
              <div v-if="ticketData.creator">创建人: {{ ticketData.creator }}</div>
              <div v-if="ticketData.respondent">响应人: {{ ticketData.respondent }}</div>
              <div v-if="ticketData.devProcessor">开发处理人: {{ ticketData.devProcessor }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 无数据状态 -->
      <div v-else class="p-8 text-center text-muted-foreground">
        <ArchiveX class="mx-auto h-12 w-12" />
        <h3 class="mt-4 text-lg font-semibold">未选择工单</h3>
        <p class="mb-4 mt-2 text-sm">
          请从左侧列表中选择一个工单来查看详情。
        </p>
      </div>
    </ScrollArea>
  </div>
</template>
