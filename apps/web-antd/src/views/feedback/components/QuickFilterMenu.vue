<!-- 从list/components移动过来的QuickFilterMenu.vue -->
<script lang="ts" setup>
import type { FilterOption } from '../types/ticket';

import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@vben-core/shadcn-ui';

import { computed, ref, watch } from 'vue';

import FilterOptionsList from './FilterOptionsList.vue';

interface QuickFilterMenuProps {
  categoryKey: string;
  categoryLabel: string;
  options: FilterOption[];
  selectedValues: string[];
}

const props = defineProps<QuickFilterMenuProps>();

const emit = defineEmits<{
  (e: 'select', categoryKey: string, value: string, isMultiSelect: boolean): void;
  (e: 'remove', categoryKey: string, value: string): void;
}>();

// 下拉菜单状态
const isOpen = ref(false);

// 初始筛选条件（用于FilterOptionsList组件）
const initialFilters = computed(() => ({
  [props.categoryKey]: [...props.selectedValues],
}));

// 监听selectedValues变化
watch(
  () => props.selectedValues,
  (newValues) => {
    // 打印调试信息
    // eslint-disable-next-line no-console
    console.warn(
      'QuickFilterMenu watch selectedValues:',
      props.categoryKey,
      JSON.stringify(newValues),
      JSON.stringify(initialFilters.value),
    );
  },
  { deep: true },
);

// 处理选项切换
function handleOptionToggle(
  categoryKey: string,
  optionValue: string,
  event: Event,
  isMultiSelect = true,
) {
  // 阻止事件冒泡
  event.stopPropagation();

  // 发送选择事件
  emit('select', categoryKey, optionValue, isMultiSelect);
}

// 检查选项是否被选中
function isOptionSelected(categoryKey: string, optionValue: string): boolean {
  return props.selectedValues.includes(optionValue);
}
</script>

<template>
  <DropdownMenu v-model:open="isOpen">
    <DropdownMenuTrigger as-child>
      <slot>
        <Button variant="ghost" size="sm" class="h-auto p-0">
          {{ selectedValues.join(', ') }}
        </Button>
      </slot>
    </DropdownMenuTrigger>
    <DropdownMenuContent class="w-48" align="start">
      <FilterOptionsList
        :category-key="categoryKey"
        :options="options"
        :selected-values="selectedValues"
        @option-toggle="handleOptionToggle"
        @is-option-selected="isOptionSelected"
      />
    </DropdownMenuContent>
  </DropdownMenu>
</template>
