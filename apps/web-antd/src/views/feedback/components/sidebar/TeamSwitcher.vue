<!-- 从list/components移动过来的TeamSwitcher.vue -->
<script setup lang="ts">
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@vben-core/shadcn-ui';

import { ChevronsUpDown, Plus } from 'lucide-vue-next';
import { computed, ref } from 'vue';

defineProps<{
  teams: {
    logo: string;
    name: string;
    plan: string;
  }[];
}>();

const { isMobile } = useSidebar();

const activeTeam = ref({
  name: 'TT语音',
  logo: '/logo/tt-logo.png',
  plan: '',
});

const isOpen = ref(false);

const activeTeamData = computed(() => activeTeam.value);
</script>

<template>
  <SidebarMenu>
    <SidebarMenuItem>
      <DropdownMenu v-model:open="isOpen">
        <DropdownMenuTrigger as-child>
          <SidebarMenuButton
            size="lg"
            class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
          >
            <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
              <img
                :src="activeTeamData.logo"
                :alt="activeTeamData.name"
                class="size-4"
              />
            </div>
            <div class="grid flex-1 text-left text-sm leading-tight">
              <span class="truncate font-semibold">{{ activeTeamData.name }}</span>
              <span class="truncate text-xs">{{ activeTeamData.plan }}</span>
            </div>
            <ChevronsUpDown class="ml-auto" />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          class="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
          align="start"
          :side="isMobile ? 'bottom' : 'right'"
          :side-offset="4"
        >
          <DropdownMenuLabel class="text-xs text-muted-foreground">
            团队
          </DropdownMenuLabel>
          <DropdownMenuItem
            v-for="team in teams"
            :key="team.name"
            @click="activeTeam = team"
          >
            <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
              <img
                :src="team.logo"
                :alt="team.name"
                class="size-4"
              />
            </div>
            <div class="grid flex-1 text-left text-sm leading-tight">
              <span class="truncate font-semibold">{{ team.name }}</span>
              <span class="truncate text-xs">{{ team.plan }}</span>
            </div>
          </DropdownMenuItem>
          <DropdownMenuItem class="gap-2 p-2">
            <div class="flex size-6 items-center justify-center rounded-md border bg-background">
              <Plus class="size-4" />
            </div>
            <div class="font-medium text-muted-foreground">添加团队</div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  </SidebarMenu>
</template>
