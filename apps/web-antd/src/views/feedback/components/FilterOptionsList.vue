<!-- 从list/components移动过来的FilterOptionsList.vue -->
<script lang="ts" setup>
import type { FilterOption } from '../types/ticket';

import { Checkbox, Input } from '@vben-core/shadcn-ui';

import { Search } from 'lucide-vue-next';
import { computed, ref } from 'vue';

interface FilterOptionsListProps {
  categoryKey: string;
  options: FilterOption[];
  selectedValues: string[];
  searchKeyword?: string;
}

const props = withDefaults(defineProps<FilterOptionsListProps>(), {
  searchKeyword: '',
});

const emit = defineEmits<{
  (
    e: 'option-toggle',
    categoryKey: string,
    optionValue: string,
    event: Event,
    isMultiSelect: boolean,
  ): void;
  (e: 'is-option-selected', categoryKey: string, optionValue: string): boolean;
}>();

// 本地搜索关键字
const localSearchKeyword = ref('');

// 筛选后的选项
const filteredOptions = computed(() => {
  const keyword = localSearchKeyword.value || props.searchKeyword || '';
  if (!keyword) return props.options;

  return props.options.filter((option) =>
    option.label.toLowerCase().includes(keyword.toLowerCase()),
  );
});

// 处理选项点击
function handleOptionClick(option: FilterOption, event: Event) {
  emit('option-toggle', props.categoryKey, option.value, event, true);
}

// 检查选项是否被选中
function isSelected(option: FilterOption): boolean {
  return props.selectedValues.includes(option.value);
}
</script>

<template>
  <div class="space-y-1">
    <!-- 本地搜索框 -->
    <div v-if="options.length > 5" class="p-2">
      <div class="relative">
        <Search class="absolute left-2 top-2.5 h-3 w-3 text-muted-foreground" />
        <Input
          v-model="localSearchKeyword"
          placeholder="搜索选项..."
          class="h-7 pl-7 text-xs"
        />
      </div>
    </div>

    <!-- 选项列表 -->
    <div class="max-h-48 overflow-y-auto">
      <div
        v-for="option in filteredOptions"
        :key="option.value"
        class="flex items-center space-x-2 px-2 py-1.5 hover:bg-accent hover:text-accent-foreground cursor-pointer"
        @click="handleOptionClick(option, $event)"
      >
        <Checkbox
          :id="`${categoryKey}-${option.value}`"
          :checked="isSelected(option)"
          @click.stop="handleOptionClick(option, $event)"
        />
        <label
          :for="`${categoryKey}-${option.value}`"
          class="text-sm font-normal cursor-pointer flex-1"
        >
          {{ option.label }}
        </label>
        <span v-if="option.count" class="text-xs text-muted-foreground">
          {{ option.count }}
        </span>
      </div>

      <!-- 无结果提示 -->
      <div
        v-if="filteredOptions.length === 0"
        class="px-2 py-4 text-center text-sm text-muted-foreground"
      >
        无匹配选项
      </div>
    </div>
  </div>
</template>
