<!-- 重命名后的List.vue -->
<script lang="ts" setup>
import type { Ticket } from '../types/ticket';

import { ScrollArea } from '@vben-core/shadcn-ui';
import { computed, watch } from 'vue';

import { useRoute } from 'vue-router';

import TicketCard from './TicketCard.vue';

interface TicketListProps {
  items?: Ticket[];
  selectedTicket?: null | string;
  searchValue?: string;
}

const props = withDefaults(defineProps<TicketListProps>(), {
  items: () => [],
  selectedTicket: null,
  searchValue: '',
});

// 定义emit
const emit = defineEmits<{
  (e: 'update:selectedTicket', id: string): void;
}>();

// 路由
const route = useRoute();

// 从URL中提取工单ID
const ticketIdFromUrl = computed(() => {
  const pathSegments = route.path.split('/');
  const ticketDetailIndex = pathSegments.indexOf('ticket-detail');
  if (ticketDetailIndex !== -1 && pathSegments[ticketDetailIndex + 1]) {
    return pathSegments[ticketDetailIndex + 1];
  }
  return null;
});

// 当前选中的工单ID
const selectedTicket = computed(() => {
  return props.selectedTicket || ticketIdFromUrl.value || '';
});

// 监听URL变化，同步选中状态
watch(
  ticketIdFromUrl,
  (newId) => {
    if (newId && newId !== props.selectedTicket) {
      emit('update:selectedTicket', newId);
    }
  },
  { immediate: true },
);

// 监听工单列表变化，自动选择第一个工单
watch(
  () => props.items,
  (newItems) => {
    // 如果URL中没有工单ID或ID无效，且当前没有选中的工单，则选择第一个工单
    if (!selectedTicket.value && newItems.length > 0 && newItems[0]) {
      const firstTicket = newItems[0];
      const ticketId = firstTicket.ticketID || firstTicket.id;
      if (ticketId) {
        emit('update:selectedTicket', ticketId);
        
        // 更新浏览器URL但不触发页面刷新
        const targetPath = `/feedback/ticket-detail/${ticketId}`;
        window.history.replaceState({}, '', targetPath);
      }
    }
  },
  { immediate: true },
);

// 处理工单点击事件
function handleItemClick(id: string) {
  // 只更新选中的工单，不进行路由导航
  emit('update:selectedTicket', id);
  
  // 可选：更新浏览器URL但不触发页面刷新（用于书签和分享）
  const targetPath = `/feedback/ticket-detail/${id}`;
  window.history.replaceState({}, '', targetPath);
}

// 简化的工单列表，筛选逻辑已移到Layout层级
</script>

<template>
  <div>
    <ScrollArea class="h-[calc(100dvh-3rem-72px)]">
      <div class="flex flex-1 flex-col pt-0">
        <TicketCard
          v-for="(item, index) of props.items"
          :key="item.ticketID || item.id"
          :ticket="item"
          :is-selected="selectedTicket === (item.ticketID || item.id)"
          :index="index"
          @click="handleItemClick"
        />

        <div
          v-if="props.items.length === 0"
          class="flex h-full flex-col items-center justify-center py-8"
        >
          <div class="text-muted-foreground font-medium">暂无数据</div>
        </div>
      </div>
    </ScrollArea>
  </div>
</template>

<style scoped></style>
