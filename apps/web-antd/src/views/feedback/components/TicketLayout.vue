<!-- 这是重命名后的Layout.vue，名称更语义化 -->
<script lang="ts" setup>
import type { Ticket } from '../types/ticket';

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
  Separator,
  TooltipProvider,
} from '@vben-core/shadcn-ui';
import { computed, nextTick, onMounted, ref, watch } from 'vue';

import TicketFilterToolbar from './TicketFilterToolbar.vue';
import TicketDisplay from './TicketDisplay.vue';
import TicketList from './TicketList.vue';

interface TicketProps {
  tickets: Ticket[];
  defaultLayout?: number[];
  defaultCollapsed?: boolean;
  navCollapsedSize: number;
  activeMenu: string;
  selectedTicketId?: string;
}

const props = withDefaults(defineProps<TicketProps>(), {
  defaultCollapsed: false,
  defaultLayout: () => [26, 30, 70],
  selectedTicketId: '',
});

const emit = defineEmits<{
  (e: 'update:activeMenu', menu: string): void;
}>();

// 响应式状态
const isCollapsed = ref(props.defaultCollapsed);
const selectedTicket = ref<string>(props.selectedTicketId || '');

// 筛选相关状态
const showFilterToolbar = ref(false);
const appliedFilters = ref<Record<string, string[]>>({});

// 监听props变化
watch(
  () => props.selectedTicketId,
  (newId) => {
    if (newId && newId !== selectedTicket.value) {
      selectedTicket.value = newId;
    }
  },
  { immediate: true },
);

// 筛选后的工单列表
const filteredTicketList = computed(() => {
  let filtered = [...props.tickets];

  // 应用筛选条件
  Object.entries(appliedFilters.value).forEach(([key, values]) => {
    if (values && values.length > 0) {
      filtered = filtered.filter((ticket) => {
        const ticketValue = ticket[key as keyof Ticket];
        if (Array.isArray(ticketValue)) {
          return values.some((value) => ticketValue.includes(value));
        }
        return values.includes(String(ticketValue || ''));
      });
    }
  });

  return filtered;
});

// 使用props.activeMenu作为初始值
const selectedMenu = ref(props.activeMenu || 'all');
const ticketLists = {
  // 所有工单
  all: computed(() => filteredTicketList.value),
  // 我创建的
  created: computed(() =>
    filteredTicketList.value.filter(
      (item) => item.creator?.toLowerCase() === 'admin',
    ),
  ),
  // 我待办的
  todo: computed(() =>
    filteredTicketList.value.filter(
      (item) => item.handler?.toLowerCase() === 'admin',
    ),
  ),
  // 待处理
  pending: computed(() =>
    filteredTicketList.value.filter(
      (item) => (item.stage || item.status) === '待处理',
    ),
  ),
  // 处理中
  processing: computed(() =>
    filteredTicketList.value.filter(
      (item) => (item.stage || item.status) === '处理中',
    ),
  ),
  // 已处理
  done: computed(() =>
    filteredTicketList.value.filter(
      (item) => (item.stage || item.status) === '已处理',
    ),
  ),
};

// 当前显示的工单列表
const currentTicketList = computed(() => {
  const listKey = selectedMenu.value as keyof typeof ticketLists;
  return ticketLists[listKey]?.value || ticketLists.all.value;
});

// 当前选中的工单详情
const currentTicket = computed(() => {
  if (!selectedTicket.value) return null;
  return (
    props.tickets.find(
      (ticket) =>
        ticket.ticketID === selectedTicket.value ||
        ticket.id === selectedTicket.value,
    ) || null
  );
});

// 监听activeMenu变化
watch(
  () => props.activeMenu,
  (newMenu) => {
    if (newMenu !== selectedMenu.value) {
      selectedMenu.value = newMenu;
    }
  },
  { immediate: true },
);

// 处理菜单变化
function handleMenuChange(menu: string) {
  selectedMenu.value = menu;
  emit('update:activeMenu', menu);
}

// 处理工单选择
function handleTicketSelect(ticketId: string) {
  selectedTicket.value = ticketId;
}

// 处理筛选条件变化
function handleFiltersChange(filters: Record<string, string[]>) {
  appliedFilters.value = { ...filters };
}

// 切换筛选工具栏显示
function toggleFilterToolbar() {
  showFilterToolbar.value = !showFilterToolbar.value;
}

// 组件挂载时的初始化
onMounted(async () => {
  await nextTick();
  
  // 如果没有选中的工单且有工单列表，选择第一个
  if (!selectedTicket.value && currentTicketList.value.length > 0) {
    const firstTicket = currentTicketList.value[0];
    if (firstTicket) {
      selectedTicket.value = firstTicket.ticketID || firstTicket.id;
    }
  }
});
</script>

<template>
  <TooltipProvider :delay-duration="0">
    <ResizablePanelGroup
      id="ticket-layout"
      direction="horizontal"
      class="h-full max-h-[calc(100dvh-3rem)] items-stretch"
    >
      <!-- 工单列表面板 -->
      <ResizablePanel
        id="ticket-list-panel"
        :default-size="defaultLayout[1]"
        :collapsed-size="navCollapsedSize"
        :collapsible="true"
        :min-size="15"
        :max-size="40"
        :class="{
          'min-w-[50px]': isCollapsed,
          'transition-all duration-300 ease-in-out': true,
        }"
        @collapsed="isCollapsed = true"
        @expanded="isCollapsed = false"
      >
        <div class="flex h-full flex-col">
          <!-- 筛选工具栏 -->
          <TicketFilterToolbar
            v-if="showFilterToolbar"
            :applied-filters="appliedFilters"
            @filters-change="handleFiltersChange"
            @toggle-toolbar="toggleFilterToolbar"
          />
          
          <!-- 工单列表 -->
          <TicketList
            :items="currentTicketList"
            :selected-ticket="selectedTicket"
            @update:selected-ticket="handleTicketSelect"
          />
        </div>
      </ResizablePanel>

      <ResizableHandle with-handle />

      <!-- 工单详情面板 -->
      <ResizablePanel
        id="ticket-detail-panel"
        :default-size="defaultLayout[2]"
        :min-size="30"
      >
        <TicketDisplay
          :ticket="currentTicket"
          @toggle-filter="toggleFilterToolbar"
        />
      </ResizablePanel>
    </ResizablePanelGroup>
  </TooltipProvider>
</template>
