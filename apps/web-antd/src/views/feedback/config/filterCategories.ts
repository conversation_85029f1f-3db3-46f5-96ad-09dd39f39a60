import type { FilterCategory } from '../types/ticket';

/**
 * 筛选类别配置
 */
export const filterCategories: FilterCategory[] = [
  {
    label: '状态',
    key: 'status',
    options: [
      { label: '待处理', value: '待处理' },
      { label: '处理中', value: '处理中' },
      { label: '已处理', value: '已处理' },
      { label: '已归档', value: '已归档' },
    ],
  },
  {
    label: '处理人',
    key: 'handler',
    options: [
      { label: '张伟', value: '张伟' },
      { label: '李娜', value: '李娜' },
      { label: '王磊', value: '王磊' },
      { label: '赵芳', value: '赵芳' },
      { label: '陈明', value: '陈明' },
    ],
  },
  {
    label: '标签',
    key: 'labels',
    options: [
      { label: '房间玩法', value: '房间玩法' },
      { label: '游戏体验', value: '游戏体验' },
      { label: '界面优化', value: '界面优化' },
      { label: '性能问题', value: '性能问题' },
      { label: '功能建议', value: '功能建议' },
    ],
  },
  {
    label: '应用',
    key: 'apps',
    options: [
      { label: '游戏大厅', value: '游戏大厅' },
      { label: '麻将', value: '麻将' },
      { label: '斗地主', value: '斗地主' },
      { label: '德州扑克', value: '德州扑克' },
      { label: '象棋', value: '象棋' },
    ],
  },
];

/**
 * 状态颜色映射
 */
export const STATUS_COLORS = {
  待处理: '#D73737',
  处理中: '#C98600',
  已处理: '#28A745',
  已归档: '#646469',
  default: '#0078FF',
};

/**
 * 状态颜色映射（RGB格式）
 */
export const STATUS_COLORS_RGB = {
  待处理: 'rgb(215,55,55)',
  处理中: 'rgb(201,134,0)',
  已处理: 'rgb(40,167,69)',
  已归档: 'rgb(100,100,105)',
  default: 'rgb(0,122,255)',
};

/**
 * 获取状态颜色
 * @param status 状态值
 * @returns 对应的颜色代码
 */
export function getStatusColor(status: string): string {
  return (
    STATUS_COLORS[status as keyof typeof STATUS_COLORS] || STATUS_COLORS.default
  );
}

/**
 * 获取状态颜色（RGB格式）
 * @param status 状态值
 * @returns 对应的RGB颜色代码
 */
export function getStatusColorRGB(status: string): string {
  return (
    STATUS_COLORS_RGB[status as keyof typeof STATUS_COLORS_RGB] ||
    STATUS_COLORS_RGB.default
  );
}

/**
 * 获取类别标签
 * @param key 类别键
 * @returns 类别标签
 */
export function getCategoryLabel(key: string): string {
  const category = filterCategories.find((cat) => cat.key === key);
  return category ? category.label : key;
}
