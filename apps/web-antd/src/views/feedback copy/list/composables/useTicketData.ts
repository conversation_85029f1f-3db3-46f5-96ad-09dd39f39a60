import type { Ticket } from '../data/tickets';

import type { TicketApiTypes } from '#/api/ticket';

import { computed, ref } from 'vue';

import { getTicketList } from '#/api/ticket';

/**
 * 工单数据管理 Composable
 */
export function useTicketData() {
  // 响应式状态
  const tickets = ref<Ticket[]>([]);
  const loading = ref(false);
  const error = ref<null | string>(null);
  const total = ref(0);
  const currentPage = ref(1);
  const limit = ref(20);

  // 计算属性
  const hasData = computed(() => tickets.value.length > 0);
  const isEmpty = computed(() => !loading.value && tickets.value.length === 0);

  /**
   * 获取工单列表
   */
  async function fetchTickets(params?: TicketApiTypes.GetTicketListParams) {
    try {
      loading.value = true;
      error.value = null;

      // 合并默认参数
      const requestParams = {
        page: currentPage.value,
        limit: limit.value,
        ...params,
      };

      console.warn('正在获取工单列表，参数:', requestParams);

      // 调用API获取数据
      const response = await getTicketList(requestParams);

      console.warn('API响应:', response);

      // 处理真实API的响应格式
      if (response && typeof response === 'object') {
        // 检查是否是真实API的响应格式 {code, msg, data}
        if ('code' in response && 'data' in response) {
          const apiResponse = response as any;

          if (apiResponse.code === 20_000) {
            // 成功响应
            const data = apiResponse.data;
            if (data && Array.isArray(data.list)) {
              // 处理API返回的工单数据，确保字段映射正确
              tickets.value = data.list.map((ticket: any) => ({
                ...ticket,
                // 确保ticketID字段存在（优先使用API返回的ticketID）
                ticketID: ticket.ticketID || ticket.id,
                // 确保id字段存在（用于兼容性）
                id: ticket.ticketID || ticket.id,
                // 统一状态字段
                status: ticket.stage || ticket.status,
                // 确保标签字段
                labels: ticket.labels || ticket.functionType || [],
                // 确保时间字段
                createdAt: ticket.enterTime || ticket.createdAt || ticket.date,
                // 确保描述字段
                text: ticket.problemDescription || ticket.text || ticket.title,
              }));
              total.value = data.total || data.list.length;
              currentPage.value = data.page || currentPage.value;
              limit.value = data.limit || data.pageSize || limit.value;
            } else if (Array.isArray(data)) {
              // 如果data直接是数组
              tickets.value = data;
              total.value = data.length;
            } else {
              console.warn('API返回数据格式不正确');
              tickets.value = [];
              total.value = 0;
            }
          } else {
            // API返回错误
            const errorMsg =
              apiResponse.msg || apiResponse.message || '获取工单列表失败';
            console.warn('API返回错误:', errorMsg);

            // 特殊处理认证错误
            if (apiResponse.code === 2002) {
              error.value = `用户未登录: ${errorMsg}。系统将自动跳转到登录页面。`;
            } else if (apiResponse.code === 2004) {
              error.value = `Token无效: ${errorMsg}。请重新登录。`;
            } else {
              error.value = errorMsg;
            }

            // 清空数据
            tickets.value = [];
            total.value = 0;
          }
        } else if ('list' in response) {
          // Mock API的响应格式
          tickets.value = response.list.map((ticket: any) => ({
            ...ticket,
            // 确保ticketID字段存在（优先使用API返回的ticketID）
            ticketID: ticket.ticketID || ticket.id,
            // 确保id字段存在（用于兼容性）
            id: ticket.ticketID || ticket.id,
            // 统一状态字段
            status: ticket.stage || ticket.status,
            // 确保标签字段
            labels: ticket.labels || ticket.functionType || [],
            // 确保时间字段
            createdAt: ticket.enterTime || ticket.createdAt || ticket.date,
            // 确保描述字段
            text: ticket.problemDescription || ticket.text || ticket.title,
          }));
          total.value = response.total || 0;
          currentPage.value = response.page || 1;
          limit.value = response.limit || response.pageSize || 20;
        } else {
          console.warn('未知的API响应格式');
          tickets.value = [];
          total.value = 0;
        }
      } else {
        console.warn('API响应为空或格式错误');
        tickets.value = [];
        total.value = 0;
      }
    } catch (error_) {
      console.error('获取工单列表失败:', error_);
      error.value =
        error_ instanceof Error ? error_.message : '获取工单列表失败';

      // 发生错误时清空数据
      tickets.value = [];
      total.value = 0;
    } finally {
      loading.value = false;
    }
  }

  /**
   * 刷新工单列表
   */
  async function refreshTickets() {
    await fetchTickets();
  }

  /**
   * 搜索工单
   */
  async function searchTickets(keyword: string) {
    await fetchTickets({ keyword });
  }

  /**
   * 根据条件筛选工单
   */
  async function filterTickets(
    filters: Partial<TicketApiTypes.GetTicketListParams>,
  ) {
    await fetchTickets(filters);
  }

  /**
   * 分页获取工单
   */
  async function loadPage(page: number, size?: number) {
    currentPage.value = page;
    if (size) {
      limit.value = size;
    }
    await fetchTickets();
  }

  /**
   * 根据ID获取单个工单
   */
  function getTicketById(id: string): Ticket | undefined {
    return tickets.value.find(
      (ticket) =>
        // 优先使用ticketID进行匹配
        ticket.ticketID === id ||
        ticket.ticketID?.toString() === id ||
        id === ticket.ticketID?.toString() ||
        // 兼容性：使用id进行匹配
        ticket.id === id ||
        ticket.id.toString() === id ||
        id === ticket.id.toString(),
    );
  }

  /**
   * 添加工单到列表（用于创建后更新）
   */
  function addTicket(ticket: Ticket) {
    tickets.value.unshift(ticket);
    total.value += 1;
  }

  /**
   * 更新工单（用于编辑后更新）
   */
  function updateTicket(updatedTicket: Ticket) {
    const index = tickets.value.findIndex(
      (ticket) => ticket.id === updatedTicket.id,
    );
    if (index !== -1) {
      tickets.value[index] = updatedTicket;
    }
  }

  /**
   * 从列表中移除工单（用于删除后更新）
   */
  function removeTicket(ticketId: string) {
    const index = tickets.value.findIndex((ticket) => ticket.id === ticketId);
    if (index !== -1) {
      tickets.value.splice(index, 1);
      total.value -= 1;
    }
  }

  return {
    // 状态
    tickets: computed(() => tickets.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    total: computed(() => total.value),
    currentPage: computed(() => currentPage.value),
    limit: computed(() => limit.value),
    hasData,
    isEmpty,

    // 方法
    fetchTickets,
    refreshTickets,
    searchTickets,
    filterTickets,
    loadPage,
    getTicketById,
    addTicket,
    updateTicket,
    removeTicket,
  };
}
