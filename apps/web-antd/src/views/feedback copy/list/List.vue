<script lang="ts" setup>
import type { Ticket } from './data/tickets';

import { ScrollArea } from '@vben-core/shadcn-ui';
import { computed, watch } from 'vue';

import { useRoute } from 'vue-router';

// 移除不再需要的导入，因为已经在 TicketCard 组件中使用
import TicketCard from './components/TicketCard.vue';

interface TicketListProps {
  items?: Ticket[];
  selectedTicket?: null | string;
  searchValue?: string;
}

const props = withDefaults(defineProps<TicketListProps>(), {
  items: () => [],
  selectedTicket: null,
  searchValue: '',
});

// 定义emit
const emit = defineEmits<{
  (e: 'update:selectedTicket', id: string): void;
}>();

// 当前选中的工单ID - 使用计算属性实现双向绑定
const selectedTicket = computed({
  get: () => props.selectedTicket || null,
  set: (value) => {
    if (value) {
      emit('update:selectedTicket', value);
    }
  },
});

// 路由
const route = useRoute();

// 初始化选中的工单
function initializeSelectedTicket() {
  // 从路由参数中获取工单ID
  const { id } = route.params;

  // 如果URL中有工单ID，优先使用它
  if (id && typeof id === 'string') {
    // 检查该ID的工单是否存在，优先使用ticketID匹配
    const ticketExists = props.items.some(
      (item) =>
        item.ticketID === id ||
        item.ticketID?.toString() === id ||
        item.id === id ||
        item.id.toString() === id,
    );
    if (ticketExists) {
      selectedTicket.value = id;
      return;
    }
  }

  // 如果URL中没有工单ID或ID无效，且当前没有选中的工单，则选择第一个工单
  if (!selectedTicket.value && props.items.length > 0 && props.items[0]) {
    const firstTicket = props.items[0];
    const ticketId = firstTicket.ticketID || firstTicket.id;
    if (ticketId) {
      selectedTicket.value = ticketId;

      // 更新浏览器URL但不触发页面刷新
      const targetPath = `/feedback/ticket-detail/${ticketId}`;
      window.history.replaceState({}, '', targetPath);
    }
  }
}

// 初始化
initializeSelectedTicket();
watch(
  () => route.params.id,
  (newId) => {
    if (newId && typeof newId === 'string') {
      // 检查该ID的工单是否存在，优先使用ticketID匹配
      const ticketExists = props.items.some(
        (item) =>
          item.ticketID === newId ||
          item.ticketID?.toString() === newId ||
          item.id === newId ||
          item.id.toString() === newId,
      );
      if (ticketExists) {
        selectedTicket.value = newId;
      }
    }
  },
);

// 处理工单点击事件
function handleItemClick(id: string) {
  // 只更新选中的工单，不进行路由导航
  selectedTicket.value = id;

  // 可选：更新浏览器URL但不触发页面刷新（用于书签和分享）
  const targetPath = `/feedback/ticket-detail/${id}`;
  window.history.replaceState({}, '', targetPath);
}

// 简化的工单列表，筛选逻辑已移到Layout层级
</script>

<template>
  <div>
    <ScrollArea class="h-[calc(100dvh-3rem-72px)]">
      <div class="flex flex-1 flex-col pt-0">
        <TicketCard
          v-for="(item, index) of props.items"
          :key="item.ticketID || item.id"
          :ticket="item"
          :is-selected="selectedTicket === (item.ticketID || item.id)"
          :index="index"
          @click="handleItemClick"
        />

        <div
          v-if="props.items.length === 0"
          class="flex h-full flex-col items-center justify-center py-8"
        >
          <div class="text-muted-foreground font-medium">暂无数据</div>
        </div>
      </div>
    </ScrollArea>
  </div>
</template>

<style scoped></style>
