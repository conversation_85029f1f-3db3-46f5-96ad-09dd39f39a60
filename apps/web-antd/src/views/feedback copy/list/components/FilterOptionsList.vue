<script lang="ts" setup>
import type { FilterOption } from '../composables/useFilterLogic';

import { Checkbox, Input } from '@vben-core/shadcn-ui';
import { Circle, Search } from 'lucide-vue-next';

import { computed, ref } from 'vue';

import { getStatusColor } from '../config/filterCategories';

// 定义组件属性
const props = defineProps<{
  // 筛选类别键
  categoryKey: string;
  // 筛选类别标签
  categoryLabel: string;
  // 筛选选项
  options: FilterOption[];
  // 当前选中的值
  selectedValues: string[];
  // 是否显示标题和搜索框
  showHeader?: boolean;
}>();

// 定义组件事件
const emit = defineEmits<{
  (e: 'toggle', key: string, value: string, isMultiSelect: boolean): void;
  (e: 'search', keyword: string): void;
}>();

// 搜索关键字
const searchKeyword = ref('');

// 筛选后的选项
const filteredOptions = computed(() => {
  if (!searchKeyword.value) return props.options;

  return props.options.filter((option) =>
    option.label.toLowerCase().includes(searchKeyword.value.toLowerCase()),
  );
});

// 检查选项是否被选中
function isOptionSelected(value: string): boolean {
  return props.selectedValues.includes(value);
}

// 处理搜索关键字变化
function handleSearchChange() {
  emit('search', searchKeyword.value);
}

// 处理选项点击 - 多选模式
function handleCheckboxToggle(value: string) {
  emit('toggle', props.categoryKey, value, true); // true表示多选模式
}
</script>

<template>
  <!-- 标题和搜索框 -->
  <div v-if="showHeader" class="border-border/20 border-b p-2">
    <div class="mb-2 px-2 py-1 text-xs font-medium">
      {{ props.categoryLabel }}
    </div>
    <div class="relative">
      <Search class="text-muted-foreground absolute left-2 top-2 size-3.5" />
      <Input
        v-model="searchKeyword"
        class="bg-muted/30 h-8 pl-8 text-sm"
        placeholder="Filter..."
        @input="handleSearchChange"
      />
    </div>
  </div>

  <div class="max-h-[300px] overflow-y-auto">
    <!-- 选项列表 -->
    <div
      v-for="option in filteredOptions"
      :key="option.value"
      class="hover:bg-muted/50 flex cursor-pointer items-center justify-between rounded-md px-3 py-2"
      @click="handleCheckboxToggle(option.value)"
    >
      <div class="flex w-full items-center gap-2">
        <!-- Checkbox组件 - 多选且菜单不消失 -->
        <Checkbox
          :checked="isOptionSelected(option.value)"
          @update:checked="() => handleCheckboxToggle(option.value)"
          @click.stop
        />

        <!-- 状态选项的特殊显示 -->
        <div
          v-if="props.categoryKey === 'status'"
          class="size-3.5 rounded-full"
          :style="`background-color: ${option.color || getStatusColor(option.value)}`"
        />

        <!-- 其他选项的图标 -->
        <Circle
          v-else-if="!option.icon"
          class="text-muted-foreground size-3.5"
        />
        <component
          :is="option.icon"
          v-else
          class="text-muted-foreground size-3.5"
        />

        <!-- 选项文字 -->
        <span class="flex-1 text-sm">
          {{ option.label }}
        </span>

        <!-- 数量显示 -->
        <span v-if="option.count" class="text-muted-foreground ml-1 text-xs">
          {{ option.count }} issue
        </span>
      </div>
    </div>
  </div>
</template>
