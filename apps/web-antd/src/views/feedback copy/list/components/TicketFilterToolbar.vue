<script lang="ts" setup>
import type { Ticket } from '../data/tickets';

import { Button } from '@vben-core/shadcn-ui';

import { computed } from 'vue';

import { useFilterLogic } from '../composables/useFilterLogic';
// 导入筛选配置
import {
  filterCategories,
  getCategoryLabel,
  getStatusColor,
} from '../config/filterCategories';
import FilterDropdown from './FilterDropdown.vue';
import QuickFilterMenu from './QuickFilterMenu.vue';

interface TicketFilterToolbarProps {
  tickets?: Ticket[];
  searchValue?: string;
}

const props = withDefaults(defineProps<TicketFilterToolbarProps>(), {
  tickets: () => [],
  searchValue: '',
});

// 定义emit
const emit = defineEmits<{
  (e: 'filter', filters: Record<string, string[]>): void;
  (e: 'update:searchValue', value: string): void;
}>();

// 搜索值的双向绑定
const searchValue = computed({
  get: () => props.searchValue || '',
  set: (value) => emit('update:searchValue', value),
});

// 使用共享的筛选逻辑
const {
  selectedFilters: activeFilters,
  toggleFilter,
  clearCategoryFilters,
  clearAllFilters,
} = useFilterLogic({}, (filters) => {
  // 向父组件发送筛选事件
  emit('filter', filters);
});

// 是否有活跃的筛选条件
const hasActiveFilters = computed(() => {
  return Object.values(activeFilters.value).some(
    (arr) => Array.isArray(arr) && arr.length > 0,
  );
});

// 处理快速筛选菜单的选择事件
function handleQuickFilterSelect(
  categoryKey: string,
  value: string,
  isMultiSelect = false,
) {
  // 调用共享的toggleFilter函数，传递isMultiSelect参数
  toggleFilter(categoryKey, value, undefined, isMultiSelect);
}

// 处理快速筛选菜单的移除事件
function handleQuickFilterRemove(categoryKey: string, _value: string) {
  // 直接调用clearCategoryFilters函数
  clearCategoryFilters(categoryKey);
}

// 处理筛选事件
function handleFilter(filters: Record<string, string[]>) {
  // 创建一个全新的对象，而不是基于当前的activeFilters
  const newFilters: Record<string, string[]> = {};

  // 更新筛选条件
  for (const [key, values] of Object.entries(filters)) {
    if (Array.isArray(values)) {
      newFilters[key] = [...values];
    }
  }

  // 更新筛选条件
  activeFilters.value = newFilters;
}

// 处理清除所有筛选条件
function handleClearAllFilters() {
  // 调用clearAllFilters函数
  clearAllFilters();
}

// 处理测试筛选 - 一键选中所有筛选项
function handleTestFilters() {
  const testFilters: Record<string, string[]> = {};

  // 遍历所有筛选类别，选中每个类别的所有选项
  for (const category of filterCategories) {
    if (category.options && category.options.length > 0) {
      testFilters[category.key] = category.options.map(
        (option) => option.value,
      );
    }
  }

  // 更新筛选条件
  Object.assign(activeFilters.value, testFilters);
  // 触发筛选事件
  emit('filter', activeFilters.value);
}

// 计算所有标签
const allTags = computed(() => {
  const tags: Array<{ key: string; values: string[] }> = [];
  for (const [key, values] of Object.entries(activeFilters.value)) {
    if (Array.isArray(values) && values.length > 0) {
      tags.push({ key: String(key), values });
    }
  }
  return tags;
});
</script>

<template>
  <div class="flex flex-col">
    <!-- 第一行：筛选按钮和操作按钮 -->
    <div class="flex items-center px-3 py-2">
      <!-- 左侧：筛选按钮 -->
      <div class="flex items-center gap-2">
        <FilterDropdown
          v-model:search-value="searchValue"
          :categories="filterCategories"
          :initial-filters="activeFilters"
          @filter="handleFilter"
        />
      </div>

      <div class="flex items-center gap-2">
        <Button
          v-if="hasActiveFilters"
          variant="ghost"
          size="sm"
          class="text-primary hover:text-primary text-xs"
          @click="handleClearAllFilters"
        >
          清除所有筛选
        </Button>

        <Button
          variant="ghost"
          size="sm"
          class="text-primary hover:text-primary text-xs"
          @click="handleTestFilters"
        >
          测试筛选
        </Button>
      </div>
    </div>

    <!-- 第二行：筛选标签（仅在有筛选条件时显示） -->
    <div v-if="hasActiveFilters" class="px-4 pb-2">
      <div class="flex flex-wrap items-center gap-2">
        <!-- 显示的标签 -->
        <template v-for="tag in allTags" :key="tag.key">
          <div
            class="border-primary/20 bg-background/80 hover:bg-background flex items-center gap-1 whitespace-nowrap rounded-md border px-3 py-1.5 text-xs transition-all duration-200 hover:shadow-sm"
          >
            <!-- 类别标签，不可点击 -->
            <span class="font-medium">
              {{ getCategoryLabel(tag.key) }}
            </span>
            <span class="text-muted-foreground">is</span>
            <!-- 值标签，可点击 -->
            <QuickFilterMenu
              :category-key="tag.key"
              :category-label="getCategoryLabel(tag.key)"
              :options="
                filterCategories.find((cat) => cat.key === tag.key)?.options ||
                []
              "
              :selected-values="tag.values"
              @select="handleQuickFilterSelect"
              @remove="handleQuickFilterRemove"
            >
              <span
                class="hover:text-primary flex cursor-pointer items-center gap-1"
              >
                <!-- 多个值时显示用分隔符连接的值 -->
                <template v-if="tag.values.length > 1">
                  <!-- 如果是状态类型，显示带颜色的状态 -->
                  <template v-if="tag.key === 'status'">
                    <span
                      class="hover:text-primary flex cursor-pointer items-center gap-1"
                    >
                      <template
                        v-for="(value, index) in tag.values"
                        :key="value"
                      >
                        <span class="flex items-center gap-1">
                          <span
                            class="inline-block size-2 rounded-full"
                            :style="{
                              backgroundColor: getStatusColor(String(value)),
                            }"
                          />
                          <span>{{ value }}</span>
                        </span>
                        <span
                          v-if="index < tag.values.length - 1"
                          class="text-muted-foreground"
                        >
                          •
                        </span>
                      </template>
                    </span>
                  </template>
                  <!-- 其他类型正常显示 -->
                  <template v-else>
                    <span
                      class="hover:text-primary max-w-48 cursor-pointer truncate"
                    >
                      {{ tag.values.join(' • ') }}
                    </span>
                  </template>
                </template>
                <!-- 单个值时显示具体值 -->
                <template v-else>
                  <span
                    v-if="tag.key === 'status'"
                    class="mr-1 inline-block size-2 rounded-full"
                    :style="{
                      backgroundColor: getStatusColor(String(tag.values[0])),
                    }"
                  />
                  <span>{{ tag.values[0] }}</span>
                </template>
              </span>
            </QuickFilterMenu>
            <Button
              variant="ghost"
              size="icon"
              class="ml-1 h-4 w-4 rounded-full p-0 transition-colors duration-200 hover:bg-red-100 hover:text-red-600"
              @click="clearCategoryFilters(tag.key)"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="10"
                height="10"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path d="M18 6 6 18" />
                <path d="m6 6 12 12" />
              </svg>
            </Button>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
