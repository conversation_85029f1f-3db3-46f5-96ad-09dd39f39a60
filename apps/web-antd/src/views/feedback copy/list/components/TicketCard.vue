<script lang="ts" setup>
import type { Ticket } from '../data/tickets';

import { cn } from '@vben/utils';

import { formatDistanceToNow } from 'date-fns';
import { Clock, User } from 'lucide-vue-next';

import { getStatusColorRGB } from '../config/filterCategories';

interface TicketCardProps {
  ticket: Ticket;
  isSelected?: boolean;
  index?: number;
}

interface TicketCardEmits {
  (e: 'click', id: string): void;
}

const props = withDefaults(defineProps<TicketCardProps>(), {
  isSelected: false,
  index: 0,
});

const emit = defineEmits<TicketCardEmits>();

function handleClick() {
  // 优先使用ticketID，兼容id
  emit('click', props.ticket.ticketID || props.ticket.id);
}
</script>

<template>
  <div
    v-motion
    :initial="{
      opacity: 0,
      y: 20,
      scale: 0.95,
    }"
    :enter="{
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 300,
        delay: index * 50,
      },
    }"
    :leave="{
      opacity: 0,
      y: -20,
      scale: 0.95,
      transition: {
        duration: 200,
      },
    }"
    :hovered="{
      y: 0,
      scale: 1,
      transition: {
        duration: 150,
        ease: 'easeOut',
      },
    }"
    :class="
      cn(
        'hover:bg-secondary/50 border-border/40 cursor-pointer overflow-hidden border-b border-l-4 border-l-transparent last:border-b-0 hover:shadow-sm',
        isSelected ? 'bg-primary/5 border-l-primary/80' : '',
      )
    "
    @click="handleClick"
  >
    <!-- 标题区域 -->
    <div class="px-3 pb-2 pl-3 pt-3">
      <!-- 标题和状态点 -->
      <div class="flex items-center gap-2">
        <!-- 状态点 -->
        <div
          class="size-2.5 shrink-0 rounded-full"
          :style="{
            backgroundColor: getStatusColorRGB(
              ticket.stage || ticket.status || '',
            ),
          }"
        ></div>

        <!-- 标题 -->
        <h5 class="line-clamp-1 text-sm font-medium">
          {{
            ticket.problemDescription ||
            ticket.title ||
            ticket.text ||
            `工单#${ticket.ticketID || ticket.id}`
          }}
        </h5>
      </div>

      <!-- ID作为副标题 -->
      <div class="text-muted-foreground mt-1 text-xs">
        TicketID#{{ ticket.ticketID || ticket.id }}
      </div>
    </div>

    <div class="px-3 pb-3 pl-3">
      <!-- 底部信息区 -->
      <div
        class="text-muted-foreground flex items-center justify-between text-xs font-normal"
      >
        <!-- 创建人 -->
        <div class="flex items-center gap-1">
          <User class="text-primary/70 size-3" />
          <span class="truncate">{{ ticket.creator || '未知' }}</span>
        </div>

        <!-- 创建时间 -->
        <div class="flex items-center gap-1">
          <Clock class="text-primary/70 size-3" />
          <span class="truncate">{{
            formatDistanceToNow(
              new Date(
                ticket.enterTime ||
                  ticket.createdAt ||
                  ticket.date ||
                  new Date(),
              ),
              { addSuffix: true },
            )
          }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* TicketCard 组件的样式 */
</style>
