<script lang="ts" setup>
import type { FilterCategory } from '../composables/useFilterLogic';

import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
  Input,
} from '@vben-core/shadcn-ui';
import { Filter, Search } from 'lucide-vue-next';

import { computed, ref, watch } from 'vue';

import { useFilterLogic } from '../composables/useFilterLogic';
import FilterOptionsList from './FilterOptionsList.vue';

// 定义组件属性
const props = withDefaults(
  defineProps<{
    // 筛选类别
    categories?: FilterCategory[];
    // 初始筛选条件
    initialFilters?: Record<string, any>;
    // 搜索值
    searchValue?: string;
  }>(),
  {
    categories: () => [],
    initialFilters: () => ({}),
    searchValue: '',
  },
);

// 定义组件事件
const emit = defineEmits<{
  (e: 'filter', filters: Record<string, string[]>): void;
  (e: 'update:filters', filters: Record<string, string[]>): void;
  (e: 'update:searchValue', value: string): void;
}>();

// 当前选中的类别
const selectedCategory = ref<null | string>(null);

// 筛选条件变化时的回调函数
function handleFilterChange(filters: Record<string, string[]>) {
  // 只触发filter事件，不再触发update:filters事件
  emit('filter', filters);
}

// 工单搜索值的双向绑定
const ticketSearchValue = computed({
  get: () => props.searchValue || '',
  set: (value) => emit('update:searchValue', value),
});

// 使用共享的筛选逻辑
const {
  isOpen,
  searchKeyword,
  selectedFilters,
  selectedCount: getSelectedCount,
  toggleFilter,
  updateFilters,
  updateFiltersSilently,
} = useFilterLogic(props.initialFilters, handleFilterChange);

// 初始化选中的筛选项
function initializeFilters() {
  const newFilters: Record<string, string[]> = {};

  // 先确保所有类别都有对应的数组
  for (const category of props.categories || []) {
    newFilters[category.key] = [];
  }

  // 如果有初始筛选条件，应用它们
  if (props.initialFilters) {
    for (const [key, values] of Object.entries(props.initialFilters)) {
      if (Array.isArray(values) && values.length > 0) {
        newFilters[key] = [...values];
      }
    }
  }

  // 更新筛选条件
  updateFilters(newFilters);
}

// 初始化
initializeFilters();

// 监听初始筛选条件的变化
watch(
  () => props.initialFilters,
  (newVal) => {
    // 检查是否真的需要更新，避免无限循环
    const currentFilters = selectedFilters.value;
    const newFiltersStr = JSON.stringify(newVal || {});
    const currentFiltersStr = JSON.stringify(currentFilters);

    // 如果状态已经一致，不需要更新
    if (newFiltersStr === currentFiltersStr) {
      return;
    }

    // 直接更新内部筛选状态，但不触发回调
    const newFilters: Record<string, string[]> = {};

    // 先确保所有类别都有对应的数组
    for (const category of props.categories || []) {
      newFilters[category.key] = [];
    }

    // 如果有新的筛选条件，应用它们
    if (newVal) {
      for (const [key, values] of Object.entries(newVal)) {
        if (Array.isArray(values) && values.length > 0) {
          newFilters[key] = [...values];
        }
      }
    }

    // 使用静默更新，不触发回调避免循环
    updateFiltersSilently(newFilters);
  },
  { deep: true, immediate: true },
);

// 处理选项切换
function handleToggle(key: string, value: string, isMultiSelect: boolean) {
  toggleFilter(key, value, undefined, isMultiSelect);
}
</script>

<template>
  <DropdownMenu v-model:open="isOpen">
    <DropdownMenuTrigger as-child>
      <Button
        variant="ghost"
        size="sm"
        class="border-border/50 bg-background flex h-8 items-center gap-1.5 px-2"
      >
        <Filter class="size-3.5" />
        筛选
        <span
          v-if="getSelectedCount > 0"
          class="bg-primary/10 text-primary ml-1 rounded-full px-1.5 text-xs"
        >
          {{ getSelectedCount }}
        </span>
      </Button>
    </DropdownMenuTrigger>

    <DropdownMenuContent class="w-[280px] p-0 shadow-lg" align="start">
      <!-- 工单搜索框 -->
      <div class="border-border/20 border-b p-2">
        <div class="relative">
          <Search
            class="text-muted-foreground absolute left-2 top-2 size-3.5"
          />
          <Input
            v-model="ticketSearchValue"
            class="bg-muted/30 h-8 pl-8 text-sm"
            placeholder="Search tickets..."
          />
        </div>
      </div>

      <!-- 筛选选项搜索框 -->
      <div class="border-border/20 border-b p-2">
        <div class="relative">
          <Search
            class="text-muted-foreground absolute left-2 top-2 size-3.5"
          />
          <Input
            v-model="searchKeyword"
            class="bg-muted/30 h-8 pl-8 text-sm"
            placeholder="Filter options..."
          />
        </div>
      </div>

      <!-- 筛选类别列表 -->
      <div class="max-h-[400px] overflow-y-auto">
        <DropdownMenuGroup>
          <div v-for="category in props.categories || []" :key="category.key">
            <!-- 为每个类别添加子菜单 -->
            <DropdownMenuSub>
              <DropdownMenuSubTrigger
                class="hover:bg-muted/50 flex cursor-pointer items-center justify-between px-3 py-2"
                :class="{ 'bg-muted/30': selectedCategory === category.key }"
              >
                <div class="flex items-center gap-2 text-sm">
                  <component
                    :is="category.icon"
                    v-if="category.icon"
                    class="text-muted-foreground size-3.5"
                  />
                  <span>{{ category.label }}</span>
                </div>
              </DropdownMenuSubTrigger>

              <DropdownMenuSubContent class="w-[280px] p-0">
                <!-- 使用共享的筛选选项列表组件 -->
                <FilterOptionsList
                  :category-key="category.key"
                  :category-label="category.label"
                  :options="category.options || []"
                  :selected-values="selectedFilters[category.key] || []"
                  :show-header="true"
                  @toggle="handleToggle"
                />
              </DropdownMenuSubContent>
            </DropdownMenuSub>
          </div>
        </DropdownMenuGroup>
      </div>
    </DropdownMenuContent>
  </DropdownMenu>
</template>
