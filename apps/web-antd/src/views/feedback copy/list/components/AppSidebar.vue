<script setup lang="ts">
import { computed } from 'vue';

import {
  ScrollArea,
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from '@vben-core/shadcn-ui';

import {
  BookOpen,
  Bot,
  Frame,
  Map,
  PieChart,
  Settings2,
  SquareTerminal,
} from 'lucide-vue-next';

import NavMain from './NavMain.vue';
import NavProjects from './NavProjects.vue';
import NavUser from './NavUser.vue';
import TeamSwitcher from './TeamSwitcher.vue';

const props = withDefaults(
  defineProps<{
    class?: string;
    collapsible?: 'icon' | 'none' | 'offcanvas';
    side?: 'left' | 'right';
    variant?: 'floating' | 'inset' | 'sidebar';
  }>(),
  {
    class: '',
    side: 'left',
    variant: 'sidebar',
    collapsible: 'icon',
  },
);

// 用户数据 - 临时数据，可以后续接入真实的用户store
const userData = computed(() => {
  return {
    name: 'Admin',
    email: '<EMAIL>',
    avatar: '/avatars/user.png',
    TT_id: 'admin',
  };
});

// 团队数据
const teamsData = [
  {
    name: 'TT语音',
    logo: '/logo/tt-logo.png',
    plan: '',
  },
  {
    name: '唱鸭',
    logo: '/logo/changya-logo.png',
    plan: '',
  },
];

// 主导航数据
const navMainData = [
  {
    title: '工单管理',
    url: '#',
    icon: SquareTerminal,
    isActive: true,
    items: [
      {
        title: '所有工单',
        url: '/feedback',
      },
      {
        title: '我创建的',
        url: '/feedback/created',
      },
      {
        title: '我待办的',
        url: '/feedback/todo',
      },
    ],
  },
  {
    title: '工单状态',
    url: '#',
    icon: Bot,
    items: [
      {
        title: '待处理',
        url: '#',
      },
      {
        title: '处理中',
        url: '#',
      },
      {
        title: '已处理',
        url: '#',
      },
      {
        title: '已归档',
        url: '#',
      },
    ],
  },
  {
    title: '统计分析',
    url: '#',
    icon: BookOpen,
    items: [
      {
        title: '工单趋势',
        url: '#',
      },
      {
        title: '处理效率',
        url: '#',
      },
      {
        title: '问题分类',
        url: '#',
      },
    ],
  },
  {
    title: '系统设置',
    url: '#',
    icon: Settings2,
    items: [
      {
        title: '工单设置',
        url: '#',
      },
      {
        title: '用户管理',
        url: '#',
      },
      {
        title: '权限配置',
        url: '#',
      },
    ],
  },
];

// 项目数据
const projectsData = [
  {
    name: '客户端反馈',
    url: '#',
    icon: Frame,
  },
  {
    name: '服务端问题',
    url: '#',
    icon: PieChart,
  },
  {
    name: '产品建议',
    url: '#',
    icon: Map,
  },
];

// 组合数据
const data = computed(() => ({
  user: userData.value,
  teams: teamsData,
  navMain: navMainData,
  projects: projectsData,
}));
</script>

<template>
  <Sidebar v-bind="props">
    <SidebarHeader>
      <TeamSwitcher :teams="data.teams" />
    </SidebarHeader>
    <SidebarContent>
      <ScrollArea class="flex h-[calc(100dvh-30px-3rem-53px)]">
        <NavMain :items="data.navMain" />
        <NavProjects :projects="data.projects" />
      </ScrollArea>
    </SidebarContent>
    <SidebarFooter>
      <NavUser :user="data.user" />
    </SidebarFooter>
    <SidebarRail />
  </Sidebar>
</template>
