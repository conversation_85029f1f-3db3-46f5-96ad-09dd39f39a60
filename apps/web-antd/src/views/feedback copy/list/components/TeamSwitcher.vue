<script setup lang="ts">
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@vben-core/shadcn-ui';
import { ChevronsUpDown } from 'lucide-vue-next';

import { ref } from 'vue';

const props = defineProps<{
  teams: {
    logo: string;
    name: string;
    plan: string;
  }[];
}>();

const { isMobile } = useSidebar();
const activeTeam = ref(props.teams[0]);
</script>

<template>
  <SidebarMenu>
    <SidebarMenuItem>
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <SidebarMenuButton
            size="lg"
            class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
          >
            <div
              class="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg"
            >
              <img :src="activeTeam?.logo" class="size-6" />
            </div>
            <div class="grid flex-1 text-left text-sm leading-tight">
              <span class="truncate font-semibold">
                {{ activeTeam?.name }}
              </span>
              <span class="truncate text-xs">{{ activeTeam?.plan }}</span>
            </div>
            <ChevronsUpDown class="ml-auto" />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          class="w-[--reka-dropdown-menu-trigger-width] min-w-56 rounded-lg"
          align="start"
          :side="isMobile ? 'bottom' : 'right'"
          :side-offset="4"
        >
          <DropdownMenuLabel class="text-muted-foreground text-xs">
            Apps
          </DropdownMenuLabel>
          <DropdownMenuItem
            v-for="team in teams"
            :key="team.name"
            class="gap-2 p-2"
            @click="activeTeam = team"
          >
            <div
              class="flex size-6 items-center justify-center rounded-sm border"
            >
              <img :src="team.logo" class="size-4 shrink-0" />
            </div>
            {{ team.name }}
            <!-- <DropdownMenuShortcut>⌘{{ index + 1 }}</DropdownMenuShortcut> -->
          </DropdownMenuItem>
          <!-- <DropdownMenuSeparator /> -->
          <!-- <DropdownMenuItem class="gap-2 p-2">
            <div
              class="bg-background flex size-6 items-center justify-center rounded-md border"
            >
              <Plus class="size-4" />
            </div>
            <div class="text-muted-foreground font-medium">Add team</div>
          </DropdownMenuItem> -->
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  </SidebarMenu>
</template>
