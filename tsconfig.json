{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@vben/ts-config/vue-app.json", "compilerOptions": {"baseUrl": ".", "declaration": false, "types": ["vite/client"], "paths": {"@/*": ["src/*"], "#/*": ["types/*"]}}, "include": ["tests/**/*.ts", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts", "types/**/*.ts", "build/**/*.ts", "build/**/*.d.ts", "mock/**/*.ts", "vite.config.ts", "src/router/routes/modules/demo/iframe.ts 副本", "src/router/routes/modules/demo/comp.ts 副本", "src/router/routes/modules/demo/system.ts 副本", "src/router/routes/modules/about.ts 副本", "src/router/routes/modules/demo/charts.ts 副本", "src/router/routes/modules/hooks/request.ts副本", "src/router/routes/modules/demo/permission.ts 副本", "src/router/routes/modules/demo/page.ts 副本", "src/router/routes/modules/demo/feat.ts 副本", "src/router/routes/modules/demo/level.ts 副本", "src/router/routes/modules/demo/steps.ts 副本", "src/router/routes/modules/demo/flow copy.ts 副本"], "exclude": ["node_modules", "tests/server/**/*.ts", "dist", "**/*.js"]}